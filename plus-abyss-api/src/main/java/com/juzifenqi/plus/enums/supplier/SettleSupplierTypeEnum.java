package com.juzifenqi.plus.enums.supplier;

import lombok.Getter;

/**
 * 主体类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum SettleSupplierTypeEnum {

    FL(1, "分流主体"), QF(2, "清分主体"), DEFAULT_QF(3, "默认清分主体");

    private final Integer code;

    private final String name;

    SettleSupplierTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static SettleSupplierTypeEnum byCode(Integer code) {
        if (code == null) {
            return null;
        }

        for (SettleSupplierTypeEnum item : SettleSupplierTypeEnum.values()) {
            if (item.code.equals(code)) {
                return item;
            }
        }

        return null;
    }

    public static String find(Integer code) {
        if (code == null) {
            return null;
        }
        SettleSupplierTypeEnum settleSupplierType = byCode(code);

        return settleSupplierType == null ? null : settleSupplierType.getName();
    }

    public static boolean fl(Integer org) {
        if (org != null && org.equals(FL.code)) {
            return true;
        }
        return false;
    }

    public static boolean qf(Integer org) {
        if (org != null && org.equals(QF.code)) {
            return true;
        }
        return false;
    }

    public static boolean defaultQf(Integer org) {
        if (org != null && org.equals(DEFAULT_QF.code)) {
            return true;
        }
        return false;
    }


}
