package com.juzifenqi.plus.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付侧退款方式枚举
 *
 * <AUTHOR>
 * @date 2024/9/2 16:11
 */
@Getter
@AllArgsConstructor
public enum PayRefundTypeEnum {

    ORIGINAL(0, "原路退款"), CHANGE_CARD(1, "换卡代付退款"), ORIGINAL_CARD(2, "原卡代付退款");

    private final Integer code;
    private final String  desc;

    public static PayRefundTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PayRefundTypeEnum e : values()) {
            if (Objects.equals(e.getCode(), code)) {
                return e;
            }
        }
        return null;
    }

    public static boolean original(Integer code) {
        if (code == null) {
            return false;
        }
        return code.equals(ORIGINAL.getCode());
    }

}
