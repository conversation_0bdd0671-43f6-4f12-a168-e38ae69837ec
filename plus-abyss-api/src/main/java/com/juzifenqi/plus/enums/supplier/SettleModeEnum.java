package com.juzifenqi.plus.enums.supplier;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/8/10
 */
@Getter
public enum SettleModeEnum {

    NORMAL(1, "普通模式"), LOOP(2, "循环模式");

    private final Integer code;

    private final String name;

    SettleModeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static boolean normal(Integer code) {
        if (code == null) {
            return false;
        }

        return code.equals(NORMAL.getCode());
    }
    public static boolean loop(Integer code) {
        if (code == null) {
            return false;
        }

        return code.equals(LOOP.getCode());
    }



}
