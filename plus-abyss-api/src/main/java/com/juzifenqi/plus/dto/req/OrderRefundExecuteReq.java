package com.juzifenqi.plus.dto.req;

import java.io.Serializable;
import lombok.Data;

/**
 * 订单退卡执行
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/24 10:22
 */
@Data
public class OrderRefundExecuteReq implements Serializable {

    private static final long serialVersionUID = 3173161779116539332L;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 处理状态 1_待处理 2_处理中 3_处理成功 4_处理失败
     */
    private Integer dealState;

    /**
     * 退卡方式
     */
    private Integer refundType;

    /**
     * 每次执行数量
     */
    private Integer size;

    /**
     * 是否重试执行
     */
    private boolean retry;
}
