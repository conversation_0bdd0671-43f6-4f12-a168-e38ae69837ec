package com.juzifenqi.plus.dto.req;


import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 代付打款
 *
 * <AUTHOR>
 * @date 2023/9/21 08:53 下午
 */
@Data
public class DefrayPayReq implements Serializable {

    private static final long serialVersionUID = 7863431157209532999L;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 返现流水id
     */
    private Integer recordId;

    /**
     * 退款比例
     */
    private BigDecimal ratio;

    /**
     * 银行卡id
     */
    private Integer cardId;

    /**
     * 加密银行卡号
     */
    private String ctCardNo;
    /**
     * 开户姓名
     */
    private String customerName;

    /**
     * 操作人ID
     */
    private Integer    optId;

    /**
     * 操作人用户名
     */
    private String     optName;

    /**
     * 备注
     */
    private String remark;
}
