package com.juzifenqi.plus.dto.req.profits;

import com.juzifenqi.plus.enums.PlusModelEnum;
import java.io.Serializable;
import lombok.Data;

/**
 * 会员商品购买检查入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/10 15:13
 */
@Data
public class ProductCheckReq implements Serializable {

    private static final long serialVersionUID = 9792886036159945L;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 商品id
     */
    private Integer productId;

    /**
     * 商品sku
     */
    private String productSku;

    /**
     * 权益ID
     *
     * @see PlusModelEnum
     */
    private Integer modelId;

    /**
     * 会员单号
     */
    private String orderSn;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 渠道id
     */
    private Integer channelId;
}
