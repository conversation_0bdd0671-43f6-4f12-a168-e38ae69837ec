package com.juzifenqi.plus.dto.resp.admin;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 权益操作日志
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/15 14:02
 */
@Data
public class PlusProfitSystemLogResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 关联日志的记录ID
     */
    private Integer recordId;

    /**
     * 日志类型:1_虚拟商品权益相关
     */
    private Integer logType;

    /**
     * 操作事件:1_新增权益 2_编辑权益 3_删除权益
     */
    private Integer optEvent;

    /**
     * 操作时间
     */
    private Date optTime;

    /**
     * 创建人ID
     */
    private Integer optUserId;

    /**
     * 创建人姓名
     */
    private String optUserName;

    /**
     * 备注
     */
    private String remark;
}
