package com.juzifenqi.plus.dto.req;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 代付入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 14:00
 */
@Data
public class CreateDefrayApplyReq implements Serializable {

    private static final long serialVersionUID = -3080517203697964767L;

    /**
     * 会员订单号
     */
    private String orderSn;

    /**
     * 退款比例
     */
    private BigDecimal ratio;

    /**
     * 取消方式
     * @see com.juzifenqi.plus.enums.PlusCancelTypeEnum
     */
    private Integer cancelType;

    /**
     * 银行卡id
     */
    private Integer cardId;

    /**
     * 取消原因
     */
    private Integer cancelReason;

    /**
     * 加密银行卡号
     */
    private String ctCardNo;
    /**
     * 开户姓名
     */
    private String customerName;

    /**
     * 操作人ID
     */
    private Integer    optId;

    /**
     * 操作人用户名
     */
    private String     optName;

    /**
     * 备注
     */
    private String remark;


    private Integer optUserType;
}
