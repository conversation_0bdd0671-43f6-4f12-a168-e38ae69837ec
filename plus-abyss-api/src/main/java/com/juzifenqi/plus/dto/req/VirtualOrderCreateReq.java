package com.juzifenqi.plus.dto.req;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 虚拟权益创单入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/9 18:17
 */
@Data
public class VirtualOrderCreateReq implements Serializable {

    private static final long serialVersionUID = 1745935954535515831L;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 商品id
     */
    private Integer productId;

    /**
     * 商品sku
     */
    private String productSku;

    /**
     * 充值账号，充值方式为直充才传
     */
    private String  rechargeAccount;

    /**
     * 权益ID
     */
    private Integer modelId;
}
