package com.juzifenqi.plus.dto.req;

import java.io.Serializable;
import lombok.Data;

/**
 * 订单中心状态变更mq记录
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/22 19:29
 */
@Data
public class CreateRefundMqRecordReq implements Serializable {

    private static final long serialVersionUID = -8028970819443975284L;

    /**
     * 消息id
     */
    private String msgId;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 用户渠道id
     */
    private Integer channelId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 订单渠道
     */
    private Integer orderChannelId;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 订单节点
     */
    private String orderNode;

    /**
     * MQ消息体
     */
    private String mqMsg;
}
