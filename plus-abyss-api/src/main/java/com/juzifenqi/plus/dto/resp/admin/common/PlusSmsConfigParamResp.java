package com.juzifenqi.plus.dto.resp.admin.common;

import java.io.Serializable;
import java.sql.Date;
import lombok.Data;

/**
 * 会员类型+短信发送节点可配置参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/5 14:12
 */
@Data
public class PlusSmsConfigParamResp implements Serializable {

    private static final long serialVersionUID = 594421568827322705L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 会员
     */
    private String configName;

    /**
     * 发送节点
     */
    private Integer sendNode;

    /**
     * 发送节点名称
     */
    private String nodeName;

    /**
     * 参数key
     */
    private String paramKey;

    /**
     * 参数说明
     */
    private String paramVal;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 状态
     */
    private Integer state;
}
