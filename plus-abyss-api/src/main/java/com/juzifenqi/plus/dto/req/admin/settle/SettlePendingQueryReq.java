package com.juzifenqi.plus.dto.req.admin.settle;

import com.juzifenqi.plus.dto.req.PageEntity;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 待结算明细查询
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/6 14:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SettlePendingQueryReq extends PageEntity implements Serializable {
    private static final long serialVersionUID = 3171081921913774629L;

    /**
     * 会员单号
     */
    private String orderSn;

    /**
     * 分流主体id
     */
    private Integer shuntSupplierId;

    /**
     * 清分主体id
     */
    private Integer separateSupplierId;

    /**
     * 结算开始时间
     */
    private String settleStartTime;

    /**
     * 结算结算时间
     */
    private String settleEndTime;
}
