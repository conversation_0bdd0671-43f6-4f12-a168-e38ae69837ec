package com.juzifenqi.plus.dto.req;

import java.io.Serializable;

/**
 * 支付回调
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/13 15:48
 */
public class PlusPayCallbackReq implements Serializable {

    private static final long serialVersionUID = -1163185746501429876L;

    /**
     * 用户ID
     */
    public Integer memberId;

    /**
     * 会员订单号
     */
    public String orderSn;

    /**
     * 借款单号
     */
    private String loanOrderSn;

    /**
     * 支付流水号
     */
    public String serialNumber;

    /**
     * 支付状态 S：支付成功 F：支付失败 QS：支付成功&扣额度成功 QF：支付成功&扣额度失败
     */
    private String payStatus;

    /**
     * 入口标识 1=开通会员mq 2=支付成功mq
     */
    private Integer flag;

    public Integer getMemberId() {
        return memberId;
    }

    public void setMemberId(Integer memberId) {
        this.memberId = memberId;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public String getLoanOrderSn() {
        return loanOrderSn;
    }

    public void setLoanOrderSn(String loanOrderSn) {
        this.loanOrderSn = loanOrderSn;
    }
}
