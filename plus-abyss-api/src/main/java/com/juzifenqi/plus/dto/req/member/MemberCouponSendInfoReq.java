package com.juzifenqi.plus.dto.req.member;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 会员优惠券发放信息查询
 *
 * <AUTHOR>
 * @date 2024/8/15 10:50
 */
@Data
public class MemberCouponSendInfoReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 查询的优惠券列表
     */
    private List<CouponQueryInfo> couponList;

    @Data
    public static class CouponQueryInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 用户优惠券唯一标识
         */
        private Integer couponUserId;

        /**
         * 用户还款券唯一标识
         */
        private String couponNo;

        /**
         * 优惠券id
         */
        private Integer couponId;

        /**
         * 优惠券发券时间
         */
        private String sendCouponTime;

    }

}
