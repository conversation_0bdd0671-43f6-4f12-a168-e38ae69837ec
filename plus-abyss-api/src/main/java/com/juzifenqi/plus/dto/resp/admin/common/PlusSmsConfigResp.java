package com.juzifenqi.plus.dto.resp.admin.common;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 短信配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/6 15:43
 */
@Data
public class PlusSmsConfigResp implements Serializable {

    private static final long serialVersionUID = 5698614303316614654L;

    /**
     * id主键自增
     */
    private Integer id;

    /**
     * 渠道
     */
    private Integer channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 会员类型
     */
    private Integer configId;

    /**
     * 发送节点
     */
    private Integer sendNode;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 签名code
     */
    private String signCode;

    /**
     * 模板code码
     */
    private String templateCode;

    /**
     * 短信参数
     */
    private String smsParam;

    /**
     * 状态 0_禁用 1_启用
     */
    private Integer smsStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
