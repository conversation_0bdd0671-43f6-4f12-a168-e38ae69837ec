package com.juzifenqi.plus.dto.req;

import java.io.Serializable;

/**
 * 获取续费信息入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/19 19:42
 */
public class PlusRenewInfoReq implements Serializable {

    private static final long serialVersionUID = -9128259350961175019L;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 权益页当前小额月卡会员单号
     */
    private String orderSn;

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public Integer getConfigId() {
        return configId;
    }

    public void setConfigId(Integer configId) {
        this.configId = configId;
    }
}
