package com.juzifenqi.plus.dto.resp.profits;

import com.juzifenqi.plus.dto.resp.admin.PlusLiftAmountResp;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2024/3/1 20:12
 * @description
 */
@Data
public class CreatePlusProModelResp implements Serializable {

    private static final long serialVersionUID = -9091575481009714866L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 会员权益ID
     */
    private Integer modelId;
    /**
     * 备注  model_id:5(权益套餐id)
     */
    private String  remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date   updateTime;
    /**
     * 方案名称
     */
    private String name;

    /**
     * 权益简称
     */
    private String shortName;

    /**
     * 引导文案
     */
    private String guideCopy;

    /**
     * 省钱文案
     */
    private String savingCopy;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 规则说明
     */
    private String ruleExplain;

    /**
     * 规则配图
     */
    private String ruleImage;

    /**
     * 权益首字母-简拼
     */
    private String shortPY;

    /**
     * 权益弹窗展示状态:1：展示 0不展示'
     */
    private Integer alertState;

    /**
     * 提额信息
     */
    private PlusLiftAmountResp plusLiftAmount;
}
