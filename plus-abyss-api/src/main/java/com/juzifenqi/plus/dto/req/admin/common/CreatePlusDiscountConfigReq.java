package com.juzifenqi.plus.dto.req.admin.common;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 会员折扣配置表Vo
 *
 * <AUTHOR>
 * @date 2022/9/06 10:33
 */
@Data
public class CreatePlusDiscountConfigReq implements Serializable {

    private static final long serialVersionUID = 9054261324619635194L;

    /**
     * 主键id（编辑时用到）
     */
    private Integer id;

    /**
     * 客群名称
     */
    private String confName;

    /**
     * 折扣类型 1=客群折扣 2=会员折扣
     */
    private Integer confType;

    /**
     * 标签 1=普通客群 2=重提订单客群
     */
    private Integer confTag;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 状态
     */
    private Integer confState;

    /**
     * 折扣
     */
    private BigDecimal discountRate;

    /**
     * 时效（单位：分钟）
     */
    private Integer effectiveTime;

    /**
     * 操作人ID
     */
    private Integer optUserId;

    /**
     * 操作姓名
     */
    private String optUserName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 折扣条件
     */
    private List<CreatePlusDiscountConditionReq> conditionList;
}