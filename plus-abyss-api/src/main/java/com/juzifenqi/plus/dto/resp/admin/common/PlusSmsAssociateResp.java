package com.juzifenqi.plus.dto.resp.admin.common;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 会员配置关联表-菜单下拉框级联
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/25 20:59
 */
@Data
public class PlusSmsAssociateResp implements Serializable {

    private static final long serialVersionUID = 7205877983747214632L;

    /**
     * id主键自增
     */
    private Integer id;

    /**
     * 渠道
     */
    private Integer channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 父节点code
     */
    private Integer parentCode;

    /**
     * 自身code、唯一、与父节点关联
     */
    private Integer configCode;

    /**
     * 会员类型
     */
    private Integer configId;

    /**
     * 渠道名称
     */
    private String configName;

    /**
     * 发送节点
     */
    private Integer sendNode;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 子节点列表
     */
    private List<PlusSmsAssociateResp> childrenList;
}
