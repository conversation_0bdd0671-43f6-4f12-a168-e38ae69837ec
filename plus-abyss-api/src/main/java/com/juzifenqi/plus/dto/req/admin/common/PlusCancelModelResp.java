package com.juzifenqi.plus.dto.req.admin.common;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 无条件取消权益类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/5 14:49
 */
@Data
public class PlusCancelModelResp implements Serializable {

    private static final long serialVersionUID = 42L;

    /**
     * id
     */
    private Integer id;

    /**
     * 权益名称
     */
    private String profitName;

    /**
     * 权益类型
     */
    private Integer profitType;

    /**
     * 方案权益id（可能为空）
     */
    private Integer modelId;

    /**
     * 创建时间
     */
    private Date createTime;
}