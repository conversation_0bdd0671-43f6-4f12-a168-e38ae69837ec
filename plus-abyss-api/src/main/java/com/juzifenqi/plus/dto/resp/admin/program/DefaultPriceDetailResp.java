package com.juzifenqi.plus.dto.resp.admin.program;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 默认方案价详情
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/12 18:07
 */
@Data
public class DefaultPriceDetailResp implements Serializable {

    private static final long serialVersionUID = -1206012713243714122L;

    /**
     * 定价主表id
     */
    private Integer id;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 配置列表
     */
    private List<PlusDefaultProgramPriceResp> defaultList;
}
