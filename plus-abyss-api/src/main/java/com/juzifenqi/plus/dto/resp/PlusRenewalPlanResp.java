package com.juzifenqi.plus.dto.resp;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/8/19  9:56
 * @description
 */
@Data
public class PlusRenewalPlanResp implements Serializable {

    /**
     * 当前期数
     */
    private Integer currentPeriod;

    /**
     * 月期数
     */
    private Integer monthPeriod;

    /**
     * 计划时间
     */
    private Date planTime;

    /**
     * 实际时间
     */
    private Date actualPlanTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 月卡金额
     */
    private BigDecimal monthOrderAmount;

    /**
     * 订单编号
     */
    private String orderSn;

    /**
     * 计划状态 1待生成 2生成中 3已生成 4生成失败 5作废
     */
    private Integer planState;

    /**
     * 月卡编号
     */
    private String monthNo;

}
