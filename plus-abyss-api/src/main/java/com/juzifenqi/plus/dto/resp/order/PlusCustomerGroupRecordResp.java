package com.juzifenqi.plus.dto.resp.order;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 会员重提客群记录
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/20 15:10
 */
@Data
public class PlusCustomerGroupRecordResp implements Serializable {

    private static final long serialVersionUID = 8127085893400831897L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 借款单号
     */
    private String orderSn;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 渠道ID
     */
    private Integer channelId;

    /**
     * 重提客群mq消息
     */
    private String msgBody;

    /**
     * 差异化利率
     */
    private String diffRate;

    /**
     * 点击确认降额按钮类型 1=在页面点击的确认降额按钮  2=在二次弹窗点击的确认降额按钮
     */
    private Integer reduceQuotaButtonType;

    /**
     * 同意降低额度按钮：0未点击；1已点击
     */
    private Integer reduceQuotaButton;

    /**
     * 放弃借款按钮：0:未点击；1已点击
     */
    private Integer waiveLoanButton;

    /**
     * 结束状态：1:正常结束；2:用户存在在途订单；3:调用额度信息失败；4:用户可借额度为空；5:订单金额小于3000；6:查询差异化利率失败；7:创单失败;8:剩余借款金额不充足;
     * 9：借款订单信息不存在；10：借款订单金额不存在；11：资匹闭单失败/异常；99：重试处理中
     */
    private Integer exitStatus;

    /**
     * 异常结束原因
     */
    private String errorMsg;

    /**
     * 期数
     */
    private Integer periods;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 审核被拒单号
     */
    private String rejectOrderSn;

    /**
     * 发给订单中心的mq消息id
     */
    private String msgId;

    /**
     * 资匹闭单失败重试次数
     */
    private Integer zpRetryNum;

    /**
     * 主流程失败重试次数（存在在途订单）
     */
    private Integer retryNum;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
