package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PlusOrderCreateResp implements Serializable {

    private static final long serialVersionUID = 760687011158628188L;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * sign，订单中心创单返回
     */
    private String sign;

    /**
     * 降息卡使用，引流标识 1：引流中原
     */
    private Integer leadCode;
}
