package com.juzifenqi.plus.dto.req.profits;

import java.io.Serializable;
import lombok.Data;

/**
 * 权益商品购买限制信息入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/19 10:28
 */
@Data
public class ProfitProductCheckLimitReq implements Serializable {

    private static final long serialVersionUID = 3123891797284202352L;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 权益id
     */
    private Integer modelId;
}
