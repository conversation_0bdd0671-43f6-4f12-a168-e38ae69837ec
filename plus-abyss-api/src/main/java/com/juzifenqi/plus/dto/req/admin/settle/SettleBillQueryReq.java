package com.juzifenqi.plus.dto.req.admin.settle;

import com.juzifenqi.plus.dto.req.PageEntity;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 结算单查询
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/6 14:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SettleBillQueryReq extends PageEntity implements Serializable {
    private static final long serialVersionUID = 3171081921913774629L;

    /**
     * 结算开始时间
     */
    private String settleStartTime;

    /**
     * 结算结算时间
     */
    private String settleEndTime;
}
