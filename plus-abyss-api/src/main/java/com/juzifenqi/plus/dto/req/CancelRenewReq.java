package com.juzifenqi.plus.dto.req;

import java.io.Serializable;
import lombok.Data;

/**
 * 取消续费入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/21 14:04
 */
@Data
public class CancelRenewReq implements Serializable {

    private static final long serialVersionUID = -7397323960491741658L;

    /**
     * 会员单号
     */
    private String orderSn;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 取消续费入口 1=权益页取消续费 2=取消会员订单同时取消续费 3=续费单划扣失败取消续费
     */
    private Integer flag;

    /**
     * 取消备注
     */
    private String remark;
}
