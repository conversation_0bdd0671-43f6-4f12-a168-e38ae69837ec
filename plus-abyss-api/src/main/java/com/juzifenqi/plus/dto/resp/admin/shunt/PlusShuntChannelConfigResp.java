package com.juzifenqi.plus.dto.resp.admin.shunt;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/26  9:45
 * @description 分流配置
 */
@Data
public class PlusShuntChannelConfigResp implements Serializable {

    private Integer id;

    /**
     * 渠道id，默认0
     */
    private Integer channelId;

    /**
     * 最小订单金额
     */
    private BigDecimal minOrderAmount;

    /**
     * 最大订单金额
     */
    private BigDecimal maxOrderAmount;

    /**
     * 是否重提用户 1_否 2_是
     */
    private Integer resubmitUser;

    /**
     * 业务场景列表（1对多关系）
     */
    private List<PlusShuntSceneResp> sceneList;
}
