package com.juzifenqi.plus.dto.resp.admin.common;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 会员折扣条件表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/23 11:28
 */
@Data
public class PlusDiscountConditionResp implements Serializable {

    private static final long serialVersionUID = 6950403950123227655L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 折扣主表conf_code
     */
    private String confCode;

    /**
     * 条件字段 参考DiscountConditionFieldEnum
     */
    private String conditionField;

    /**
     * 条件key 参考DiscountConditionKeyEnum
     */
    private String conditionKey;

    /**
     * 条件值
     */
    private String conditionVal;

    /**
     * 创建时间
     */
    private Date createTime;
}
