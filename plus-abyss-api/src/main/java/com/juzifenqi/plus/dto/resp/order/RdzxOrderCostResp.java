package com.juzifenqi.plus.dto.resp.order;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 融担咨询订单及费用信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/2 14:01
 */
@Data
public class RdzxOrderCostResp implements Serializable {

    private static final long serialVersionUID = 6819943953592935774L;

    /**
     * 借款单号
     */
    private String orderSn;

    /**
     * 会员单号
     */
    private String plusOrderSn;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 还款日
     */
    private Date repayDate;

    /**
     * 1:逾期，2：普通代还
     */
    private Integer billType;

    /**
     * 逾期天数
     */
    private Integer overdueDays;

    /**
     * 会员类型
     */
    private Integer configId;

    /**
     * 会员订单状态
     */
    private Integer orderState;

    /**
     * 实际还款日
     */
    private Date actualRepay;
}
