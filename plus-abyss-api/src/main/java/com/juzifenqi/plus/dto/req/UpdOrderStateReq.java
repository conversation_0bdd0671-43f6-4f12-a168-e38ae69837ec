package com.juzifenqi.plus.dto.req;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 客服操作变更订单状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 16:13
 */
@Data
public class UpdOrderStateReq implements Serializable {

    private static final long serialVersionUID = 6283800166934294402L;

    /**
     * 会员单号
     */
    private String orderSn;

    /**
     * 变更的会员订单状态
     */
    private Integer orderState;

    /**
     * 变更原因code
     */
    private Integer changeReasonCode;

    /**
     * 线下退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 操作人ID
     */
    private Integer optUserId;

    /**
     * 操作姓名
     */
    private String optUserName;
}
