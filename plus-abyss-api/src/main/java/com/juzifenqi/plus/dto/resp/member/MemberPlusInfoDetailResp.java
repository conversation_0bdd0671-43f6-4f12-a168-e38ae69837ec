package com.juzifenqi.plus.dto.resp.member;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户会员详情信息
 *
 * <AUTHOR>
 * @date 2023/08/29 10:54
 **/
@Data
public class MemberPlusInfoDetailResp implements Serializable {

    private static final long serialVersionUID = 8162321928868048493L;

    /**
     * id
     */
    private Integer id;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 会员周期id
     */
    private Integer plusInfoId;

    /**
     * 会员类别id
     */
    private Integer configId;

    /**
     * 会员类型名称
     */
    private String configName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 周期时长
     */
    private Integer periods;

    /**
     * 周期类型  年:year  季:quarter  月:month
     */
    private String periodType;

    /**
     * 桔享会员状态  1开启  0过期    2取消
     */
    private Integer jxStatus;

    /**
     * 桔享开始时间
     */
    private Date jxStartTime;

    /**
     * 桔享结束时间
     */
    private Date jxEndTime;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 订单id
     */
    private String orderSn;

    /**
     * 会员类型  1_正常开通  2_续费
     */
    private Integer plusType;
}
