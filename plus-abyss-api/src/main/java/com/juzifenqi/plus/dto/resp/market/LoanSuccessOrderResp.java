package com.juzifenqi.plus.dto.resp.market;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;


/**
 * 放款成功记录
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/24 10:18
 */
@Data
public class LoanSuccessOrderResp implements Serializable {

    private static final long serialVersionUID = 42L;

    /**
     *
     */
    private Integer id;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 借款订单号
     */
    private String orderSn;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 订单创建时间
     */
    private Date orderCreateTime;

    /**
     * 放款时间
     */
    private Date loanTime;

    /**
     * 排队时间（分钟）
     */
    private Integer waitTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;
}