package com.juzifenqi.plus.dto.resp.admin.shunt.separate;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 清分主体
 *
 * <AUTHOR>
 */
@Data
public class SupplierSeparateInfoListResp implements Serializable {

    private static final long serialVersionUID = -483949265020640052L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 主体名称
     */
    private String supplierName;

    /**
     * 商户识别码
     */
    private String merchantId;

    /**
     * 业务场景
     */
    private String businessScene;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
