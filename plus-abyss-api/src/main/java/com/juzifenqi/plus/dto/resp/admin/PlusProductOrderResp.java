package com.juzifenqi.plus.dto.resp.admin;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 会员商品订单信息
 * <p>目前是0元商品在使用</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/6 16:50
 */
@Data
public class PlusProductOrderResp implements Serializable {

    private static final long serialVersionUID = -779225973534173039L;

    /**
     * 商品sku
     */
    private String productSku;

    /**
     * 商品订单号
     */
    private String orderSn;

    /**
     * 商品id(权益id)
     */
    private Integer productId;

    /**
     * 商品SPU
     */
    private String productSpu;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 订单金额
     */
    private BigDecimal orderMoney;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 商品订单状态说明
     */
    private String orderStateDesc;
}
