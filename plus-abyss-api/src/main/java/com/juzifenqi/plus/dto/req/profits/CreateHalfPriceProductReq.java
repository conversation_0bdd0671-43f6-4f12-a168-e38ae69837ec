package com.juzifenqi.plus.dto.req.profits;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 保存半价商品入参
 *
 * <AUTHOR>
 * @createTime 2024/2/28 17:15
 * @description
 */
@Data
public class CreateHalfPriceProductReq implements Serializable {

    private static final long serialVersionUID = -563571266475427011L;

    /**
     * id
     */
    private Integer id;

    /**
     * 商品SPU
     */
    private String productCode;

    /**
     * 商城价Mobile
     */
    private BigDecimal mallMobilePrice;

    /**
     * 商品名称
     */
    private String  name1;
    /**
     * 分类Id
     */
    private Integer productCateId;

    /**
     * 分类名称
     */
    private String productCateName;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;
    /**
     * 库存
     */
    private Integer    productStock;
    /**
     * 分类名称
     */
    private String     name;
    /**
     * 商品id
     */
    private Integer    productId;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 操作人id
     */
    private Integer optId;

    /**
     * 操作人
     */
    private String optName;
}
