package com.juzifenqi.plus.dto.resp.detail.land;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 虚拟商品明细
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13 18:12
 */
@Data
public class LandVirtualProductDetailResp implements Serializable {

    private static final long serialVersionUID = 7788146499486645002L;

    /**
     * 会员类型模板ID
     */
    private Integer configId;

    /**
     * 会员方案ID
     */
    private Integer programId;

    /**
     * 商品ID(即权益ID_product表主键)
     */
    private Integer productId;

    /**
     * 商品名称(即权益名称)
     */
    private String productName;

    /**
     * 虚拟商品ID(virtual_goods表主键)
     */
    private Integer virtualGoodsId;

    /**
     * 商品sku
     */
    private String sku;

    /**
     * 会员折扣
     */
    private BigDecimal discountRate;

    /**
     * 虚拟商品上下架 0 下架 1上架
     */
    private Integer status;

    /**
     * 排序序号
     */
    private Integer rankNum;

    /**
     * 营销图片地址
     */
    private String imgUrl;

    /**
     * 权益类型id
     */
    private Integer profitTypeId;

    /**
     * 市场价（划线价）
     */
    private BigDecimal marketPrice;

    /**
     * 售价
     */
    private BigDecimal discountPrice;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 售价
     */
    private BigDecimal sellPrice;

    /**
     * 会员价
     */
    private BigDecimal plusPrice;

    /**
     * 领取状态- 0 未领取 1 本月已领 2 会员有效期内已领  3 每周期内已领
     */
    private int receiveState;

    /**
     * 酒店订单状态 酒店订单状态， 0=无任何按钮  1=去预定酒店  2=查看酒店订单
     */
    private Integer hotelOrderBtn = 0;

    /**
     * 虚拟权益订单号
     */
    private String virtualOrderSn;
}
