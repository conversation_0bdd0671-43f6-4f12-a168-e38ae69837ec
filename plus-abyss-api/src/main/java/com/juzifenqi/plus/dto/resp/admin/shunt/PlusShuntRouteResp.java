package com.juzifenqi.plus.dto.resp.admin.shunt;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 分流路由配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/20 10:25
 */
@Data
public class PlusShuntRouteResp implements Serializable {

    private static final long serialVersionUID = -1669355081368488049L;

    /**
     * id
     */
    private Integer id;

    /**
     * 状态
     */
    private Integer enableState;

    /**
     * 主体id
     */
    private Integer supplierId;

    /**
     * 优先级
     */
    private Integer priorityOrder;

    /**
     * 分流配置
     */
    private PlusShuntConfigResp config;

    /**
     * 分流配置
     */
    private List<PlusShuntChannelConfigResp> configList;
}
