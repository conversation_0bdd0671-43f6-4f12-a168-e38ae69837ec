package com.juzifenqi.plus.dto.resp.profits;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 半价商品
 *
 * <AUTHOR>
 * @createTime 2024/2/28 17:17
 * @description
 */
@Data
public class PlusHalfPriceProductResp implements Serializable {
    private static final long serialVersionUID = -4336989375136299121L;


    /**
     * id
     */
    private Integer id;

    /**
     * 商品SPU
     */
    private String productCode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 商城价Mobile
     */
    private BigDecimal mallMobilePrice;

    /**
     * 商品名称
     */
    private String  name1;
    /**
     * 分类Id
     */
    private Integer productCateId;

    /**
     * 分类名称
     */
    private String productCateName;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;
    /**
     * 库存
     */
    private Integer    productStock;
    /**
     * 分类名称
     */
    private String     name;
    /**
     * 商品id
     */
    private Integer    productId;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 排序
     */
    private Integer sort;
}
