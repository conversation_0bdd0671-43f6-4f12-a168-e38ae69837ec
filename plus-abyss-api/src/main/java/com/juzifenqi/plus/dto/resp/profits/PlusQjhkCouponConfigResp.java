package com.juzifenqi.plus.dto.resp.profits;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 区间还款配置优惠券-列表返回
 *
 * <AUTHOR>
 * @date 2024/7/31 上午10:47
 */
@Data
public class PlusQjhkCouponConfigResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 减免金额最小金额
     */
    private BigDecimal minAmount;

    /**
     * 减免金额最大金额
     */
    private BigDecimal maxAmount;

    /**
     * 期数
     */
    private Integer periodNum;

    /**
     * 优惠券id
     */
    private Integer couponId;

    /**
     * 状态 1-启用 2-停用
     */
    private Integer state;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}
