package com.juzifenqi.plus.dto.resp.profits;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2024/2/28 10:56
 * @description
 */
@Data
public class PlusProgramProductTypeResp implements Serializable {

    private static final long serialVersionUID = -7275887024250734410L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 父级id
     */
    private Integer parentId;

    /**
     * 会员类型
     */
    private Integer configId;

    /**
     * 会员方案ID
     */
    private Integer programId;

    /**
     * 分类名称
     */
    private String typeName;

    /**
     * 分类层级 1_一级 2_二级
     */
    private Integer typeLevel;

    /**
     * 权益id
     */
    private Integer modelId;

    /**
     * 购买次数限制 0_不限制购买次数 1_每月购买一次 2_限会员有效期购买一次 3_每周期购买一次
     */
    private Integer numLimit;

    /**
     * 购买分类下权益种类数量限制 0_不限制 1_限制
     */
    private Integer typeLimit;

    /**
     * 可选分类下权益种类数量
     */
    private Integer typeLimitNum;

    /**
     * 排序序号
     */
    private Integer rankNum;

    /**
     * 营销文案
     */
    private String marketContent;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人ID
     */
    private Integer updateUserId;

    /**
     * 修改人姓名
     */
    private String updateUserNm;
}
