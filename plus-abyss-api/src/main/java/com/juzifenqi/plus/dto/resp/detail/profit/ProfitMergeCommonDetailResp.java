package com.juzifenqi.plus.dto.resp.detail.profit;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 方案信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/06/12 11:15
 */
@Data
public class ProfitMergeCommonDetailResp implements Serializable {

    private static final long serialVersionUID = -7040866166921789070L;

    /**
     * 会员提升总额度（加速+固额+桔享）
     */
    private BigDecimal vipCreditSumAll;

    /**
     * 加速审核到期时间
     */
    private String expediteEndTime;

    /**
     * 会员到期时间
     */
    private String endTime;

    /**
     * 当期会员订单
     */
    private String currentOrderSn;

    /**
     * 后付款订单号
     */
    private String orderAfterPay;

    /**
     * 权益页额度使用标识
     */
    private String operate;

    /**
     * 小额月卡取消续费入口
     */
    private boolean xeykCancelRenew;

    /**
     * 小额月卡当前会员订单状态,xeykCancelRenew为true时赋值
     */
    private Integer xeykCurrentOrderState;

    /**
     * 计划分流id
     */
    private Integer planSupplierId;

    /**
     * 支付方式
     */
    private Integer payType;

    /**
     * 剩余支付金额
     */
    private BigDecimal surplusPayAmount;
}
