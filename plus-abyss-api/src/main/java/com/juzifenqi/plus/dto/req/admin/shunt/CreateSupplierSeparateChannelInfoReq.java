package com.juzifenqi.plus.dto.req.admin.shunt;

import com.juzifenqi.plus.enums.supplier.SeparateTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/6/26  11:06
 * @description
 */
@Data
public class CreateSupplierSeparateChannelInfoReq implements Serializable {

    /**
     * 清分主体id
     */
    private Integer separateSupplierId;

    /**
     * 清分比例
     */
    private BigDecimal separateRate;

    /**
     * 清分金额
     */
    private BigDecimal separateAmount;

}
