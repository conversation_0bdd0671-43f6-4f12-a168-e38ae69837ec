package com.juzifenqi.plus.dto.req.profits;

import com.juzifenqi.plus.dto.req.PageEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2024/2/27 14:32
 * @description
 */
@Data
public class QueryVirtualGoodsReq extends PageEntity implements Serializable {

    private static final long serialVersionUID = -3148103483369965220L;

    /**
     * 会员方案ID
     */
    private Integer programId;

    /**
     * 权益类型id
     */
    private Integer profitTypeId;
}
