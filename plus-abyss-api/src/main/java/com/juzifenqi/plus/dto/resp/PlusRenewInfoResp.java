package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;

/**
 * 续费信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/19 19:40
 */
public class PlusRenewInfoResp implements Serializable {

    private static final long serialVersionUID = -4584296762817693546L;

    /**
     * 续费状态
     */
    private Integer renewState;

    /**
     * 备注
     */
    private String remark;

    /**
     * 当前周期会员单支付状态
     */
    private Integer currentOrderState;

    public Integer getCurrentOrderState() {
        return currentOrderState;
    }

    public void setCurrentOrderState(Integer currentOrderState) {
        this.currentOrderState = currentOrderState;
    }

    public Integer getRenewState() {
        return renewState;
    }

    public void setRenewState(Integer renewState) {
        this.renewState = renewState;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
