package com.juzifenqi.plus.dto.resp.member;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 用户会员周期信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/2 19:19
 */
@Data
public class MemberPlusInfoResp implements Serializable {

    private static final long serialVersionUID = 5847073519550390250L;

    private Integer id;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道id  商城1
     */
    private Integer channelId;

    /**
     * 会员类型
     */
    private Integer configId;

    /**
     * 会员方案
     */
    private Integer programId;

    /**
     * 方案单价
     */
    private BigDecimal mallMobilePrice;

    /**
     * 周期时长
     */
    private Integer periods;

    /**
     * 周期类别 年year
     */
    private String periodType;

    /**
     * 会员类型
     */
    private String memberPlusType;

    /**
     * 购买途径  1购买 2赠送
     */
    private Integer buyType;

    /**
     * 桔享状态 1正常 0过期 2取消
     */
    private Integer jxStatus;

    /**
     * 周期开始时间
     */
    private Date jxStartTime;

    /**
     * 周期结束时间
     */
    private Date jxEndTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 以下为扩展表中字段 续费状态  1_包月续费中 2_已取消续费
     */
    private Integer renewState;

    /**
     * 以下为扩展表中字段 备注
     */
    private String remark;
}
