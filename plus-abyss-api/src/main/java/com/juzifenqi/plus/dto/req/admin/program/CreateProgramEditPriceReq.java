package com.juzifenqi.plus.dto.req.admin.program;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 创建方案改价入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/3 14:45
 */
@Data
public class CreateProgramEditPriceReq implements Serializable {

    private static final long serialVersionUID = -319940140241709092L;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 修改后的价格
     */
    private BigDecimal price;

    /**
     * 修改前的划线价
     */
    private BigDecimal oldPrice;

    /**
     * 修改后的划线价
     */
    private BigDecimal linePrice;

    /**
     * 修改前的划线价
     */
    private BigDecimal oldLinePrice;

    /**
     * 执行时间
     */
    private Date executeTime;

    /**
     * 操作人id
     */
    private Integer optId;

    /**
     * 操作人名称
     */
    private String optName;
}
