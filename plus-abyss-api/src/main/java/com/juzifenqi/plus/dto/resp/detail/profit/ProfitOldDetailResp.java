package com.juzifenqi.plus.dto.resp.detail.profit;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 老权益页结构
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/18 17:46
 */
@Data
public class ProfitOldDetailResp implements Serializable {

    private static final long serialVersionUID = -6758541567101951665L;

    /**
     * 方案名称
     */
    private String name;

    /**
     * 划线价
     */
    private BigDecimal memberPrice;

    /**
     * 售价
     */
    private BigDecimal mallMobilePrice;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 会员权益数量
     */
    private Integer rightsNum;

    /**
     * 方案天数
     */
    private Integer programmeDays;

    /**
     * 会员权益页中的权益
     */
    private Map<String, Object> memberDetailProfits;

    /**
     * 会员主题 1-金色；2-紫色；3-铜色；4-蓝色；5-黑色
     */
    private Integer programColor;

    /**
     * 会员版本 1-横版；2-竖版
     * <p>目前只有竖版了</p>
     */
    private Integer programVersion = 2;

    /**
     * 是否显示会员权益页按钮 1-展示；2-不展示
     */
    private Integer showPlusButton;

    /**
     * 会员到期时间
     */
    private String endTime;

    /**
     * 专属权益到期时间
     */
    private String profitEndTime;

    /**
     * 用户登录状态 1 是登录  0是未登录
     */
    private Integer isLogin = 1;

    /**
     * 虚拟商品信息
     */
    private List<ProfitVirtualProductTypeResp> virtualProfits;

    /**
     * 营销文案
     */
    private String marketContent;

    /**
     * 省钱文案
     */
    private String amountContent;

    /**
     * 会员说明
     */
    private String userExplain;

    /**
     * 会员规则
     */
    private String userRules;

    /**
     * 认证状态  1_已认证、2_认证中 3_授权失败 4_未认证 5_认证项即将过期 7_认证项已过期 8_准入失败,创建审批单异常 13_取消认证
     */
    private Integer authState;

    /**
     * 后付款订单号
     */
    private String orderAfterPay;

    /**
     * 是否支持后付款 1支持 2不支持
     */
    private Integer afterPayState;

    /**
     * 订单id
     */
    private String orderSn;

    /**
     * 开关状态 1_开启 2_关闭
     */
    private Integer plusSwitch;
}
