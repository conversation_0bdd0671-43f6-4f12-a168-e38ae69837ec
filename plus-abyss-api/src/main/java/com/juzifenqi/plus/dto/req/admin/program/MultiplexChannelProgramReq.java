package com.juzifenqi.plus.dto.req.admin.program;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 复用渠道方案入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/12 14:04
 */
@Data
public class MultiplexChannelProgramReq implements Serializable {

    private static final long serialVersionUID = 6129209041053605085L;

    /**
     * 目标渠道id
     */
    private Integer targetChannelId;

    /**
     * 源渠道id
     */
    private Integer sourceChannelId;

    /**
     * 源会员类型id
     */
    private Integer sourceConfigId;

    /**
     * 源渠道方案id集合
     */
    private List<Integer> sourceProgramIds;

    /**
     * 操作人
     */
    private String optUser;

    /**
     * 操作人id
     */
    private Integer optUserId;
}
