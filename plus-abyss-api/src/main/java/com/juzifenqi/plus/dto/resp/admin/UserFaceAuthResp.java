package com.juzifenqi.plus.dto.resp.admin;

import java.io.Serializable;
import lombok.Data;

/**
 * 用户人脸识别信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/9 10:45
 */
@Data
public class UserFaceAuthResp implements Serializable {

    private static final long serialVersionUID = -7966393577161371514L;

    /**
     * 识别场景code
     */
    private String businessEntry;

    /**
     * 识别场景说明
     */
    private String businessEntryName;

    /**
     * 识别结果 1=通过 2=不通过
     */
    private Integer authResult;

    /**
     * 识别结果说明
     */
    private String authResultDesc;

    /**
     * 活体照片链接
     */
    private String livingPic;

    /**
     * 认证时间
     */
    private String authTime;
}
