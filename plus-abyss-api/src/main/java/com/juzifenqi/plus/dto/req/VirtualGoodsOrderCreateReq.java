package com.juzifenqi.plus.dto.req;

import java.io.Serializable;
import lombok.Data;

/**
 * 创建虚拟商品权益订单入参
 *
 * <AUTHOR>
 * @date 2024/5/27 上午11:12
 */
@Data
public class VirtualGoodsOrderCreateReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 会员订单号
     */
    private String plusOrderSn;

    /**
     * 充值账号，充值方式为直充才传
     */
    private String rechargeAccount;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 渠道id
     */
    private Integer modelId;

}
