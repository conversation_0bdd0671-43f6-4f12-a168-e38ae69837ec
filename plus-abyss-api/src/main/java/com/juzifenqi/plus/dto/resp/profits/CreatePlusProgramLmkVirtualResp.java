package com.juzifenqi.plus.dto.resp.profits;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 生活联名卡
 *
 * <AUTHOR>
 * @createTime 2024/3/1 20:13
 * @description
 */
@Data
public class CreatePlusProgramLmkVirtualResp implements Serializable {

    private static final long serialVersionUID = 7277461087397593257L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 会员类型ID
     */
    private Integer configId;

    /**
     * 会员方案ID
     */
    private Integer programId;

    /**
     * 商品ID(即权益ID_product表主键)
     */
    private Integer productId;

    /**
     * 商品名称(即权益名称)
     */
    private String productName;

    /**
     * 虚拟商品ID(virtual_goods表主键)
     */
    private Integer virtualGoodsId;

    /**
     * 商品sku
     */
    private String sku;

    /**
     * 虚拟商品上下架 0_未上架 1_上架
     */
    private Integer virtualStatus;

    /**
     * 营销图片地址
     */
    private String imgUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建人ID
     */
    private Integer createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 修改人ID
     */
    private Integer updateUserId;

    /**
     * 修改人姓名
     */
    private String updateUserName;

    /**
     * 联名卡虚拟权益售价
     */
    private BigDecimal mallMobilePrice;

    /**
     * 联名卡虚拟权益市场价
     */
    private BigDecimal marketPrice;

    /**
     * 联名卡售价（会员费+虚拟权益售价）-v4落地页计算
     */
    private BigDecimal lmkMallMobilePrice;

    /**
     * 联名卡划线价（会员费+虚拟权益市场价）-v4落地页计算
     */
    private BigDecimal lmkMarketPrice;
}
