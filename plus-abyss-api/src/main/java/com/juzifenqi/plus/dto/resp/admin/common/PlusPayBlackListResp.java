package com.juzifenqi.plus.dto.resp.admin.common;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 黑名单
 *
 * <AUTHOR>
 * @date 2022/6/30 10:39
 */
@Data
public class PlusPayBlackListResp implements Serializable {

    private static final long serialVersionUID = 443181086995141723L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 黑名单类型 1-后付款   2-会员黑名单
     */
    private Integer blackType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
