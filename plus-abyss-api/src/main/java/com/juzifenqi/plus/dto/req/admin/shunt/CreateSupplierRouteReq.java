package com.juzifenqi.plus.dto.req.admin.shunt;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 创建分流路由配置入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/19 10:52
 */
@Data
public class CreateSupplierRouteReq implements Serializable {

    private static final long serialVersionUID = 4430595416160306413L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 分流主体id
     */
    private Integer supplierId;

    /**
     * 分流主体名称
     */
    private String supplierName;

    /**
     * 优先级
     */
    private Integer priorityOrder;

    /**
     * 每日金额上限
     */
    private BigDecimal totalOrderAmount;

    /**
     * 每日笔数上限
     */
    private Integer totalOrderNum;

    /**
     * 分渠道配置
     */
    private List<CreateSupplierRouteChannelReq> supplierRouteChannelList;

    /**
     * 操作人id
     */
    private Integer optUserId;

    /**
     * 操作人名称
     */
    private String optUserName;
}
