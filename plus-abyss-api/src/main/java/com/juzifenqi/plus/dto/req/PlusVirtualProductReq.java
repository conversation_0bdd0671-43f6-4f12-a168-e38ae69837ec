package com.juzifenqi.plus.dto.req;


import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 虚拟权益详情页入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/20 09:33
 */
@Data
public class PlusVirtualProductReq implements Serializable {

    private static final long serialVersionUID = 1836151970698588754L;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 商品id
     */
    private Integer productId;

    /**
     * 商品sku
     */
    private String productSku;

    /**
     * 订单金额
     */
    private BigDecimal moneyOrder;

    /**
     * 方案名称
     */
    private String programName;

    /**
     * 充值账号
     */
    private String  rechargeAccount;
    /**
     * 订单id
     */
    private String  orderSn;
    /**
     * 权益类型id
     */
    private Integer profitTypeId;
    /**
     * 权益id
     */
    private Integer modelId;
}
