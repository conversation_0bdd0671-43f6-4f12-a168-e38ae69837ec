package com.juzifenqi.plus.dto.resp.admin;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 会员订单操作日志返回
 * @date 2024/2/2 11:24
 */
@Data
public class MemberPlusSystemLogResp implements Serializable {

    private static final long serialVersionUID = 42L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 用户id
     */
    private Integer memberId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 节点类型：1_开通会员 2_取消会员 3_多买多送 4_领取权益 5_会员提额 6_开卡礼 7_拒就赔
     */
    private Integer nodeType;

    /**
     * 取消原因 1_借款或商品单审核被拒 2_由于自身原因不想用了 3_未给用户提高额度
     */
    private Integer cancelReason;

    /**
     * 取消原因选择其他时，备注
     */
    private String cancelRemark;

    /**
     * 备注
     */
    private String remark;

    /**
     * 无条件取消-已用权益类型逗号分隔
     */
    private String useProfit;

    /**
     * 操作人ID，系统操作
     */
    private Integer operatingId;

    /**
     * 操作人
     */
    private String operatingName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 类型 1_无条件取消 2_批量取消
     */
    private Integer type;

    public MemberPlusSystemLogResp useProfit(String useProfit) {
        this.setUseProfit(useProfit);
        return this;
    }

    public MemberPlusSystemLogResp(Integer memberId, Integer channelId, String orderSn) {
        this.memberId = memberId;
        this.channelId = channelId;
        this.orderSn = orderSn;
    }

    public MemberPlusSystemLogResp(Integer memberId, Integer channelId, Integer programId,
            String orderSn) {
        this.memberId = memberId;
        this.channelId = channelId;
        this.programId = programId;
        this.orderSn = orderSn;
    }

    public MemberPlusSystemLogResp() {
    }

    public MemberPlusSystemLogResp memberId(Integer memberId) {
        this.setMemberId(memberId);
        return this;
    }

    public MemberPlusSystemLogResp channelId(Integer channelId) {
        this.setChannelId(channelId);
        return this;
    }

    public MemberPlusSystemLogResp programId(Integer programId) {
        this.setProgramId(programId);
        return this;
    }

    public MemberPlusSystemLogResp orderSn(String orderSn) {
        this.setOrderSn(orderSn);
        return this;
    }

    public MemberPlusSystemLogResp reason(Integer cancelReason) {
        this.setCancelReason(cancelReason);
        return this;
    }
    public MemberPlusSystemLogResp cancelRemark(String cancelRemark) {
        this.setCancelRemark(cancelRemark);
        return this;
    }

    public MemberPlusSystemLogResp optId(Integer operatingId) {
        this.setOperatingId(operatingId);
        return this;
    }

    public MemberPlusSystemLogResp optName(String operatingName) {
        this.setOperatingName(operatingName);
        return this;
    }
}
