package com.juzifenqi.plus.dto.req;

import com.juzifenqi.plus.dto.pojo.CreateOrderContext;
import com.juzifenqi.plus.enums.CreateOrderFlagEnum;
import java.io.Serializable;
import lombok.Data;

/**
 * 会员订单创单入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/30 11:26
 */
@Data
public class PlusOrderCreateReq implements Serializable {

    private static final long serialVersionUID = -5719732225911552801L;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 开通方式
     */
    private Integer payType;

    /**
     * 会员下单业务上下文（可选）
     */
    private CreateOrderContext createOrderContext;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 渠道标识
     */
    private Integer bizSource;

    /**
     * 三方订单号
     */
    private String outOrderSn;

    /**
     * 订单类型
     *
     * @see BusinessTypeEnum
     */
    private Integer businessType;

    /**
     * 订单归因标记-可空
     */
    private String ascribeTo;

    /**
     * 是否续费订单
     * <p>小额月卡和还款卡</p>
     *
     * @see com.juzifenqi.plus.enums.OrderRenewEnum
     */
    private Integer renew;

    /**
     * 区分还款卡：首单/续费
     * <p>1=首单 2=续费单</p>
     *
     * @see CreateOrderFlagEnum
     */
    private Integer createFlag;

    /**
     * 创单场景入口标识  1=借款首页 2=确认借款页 3=信用支付完成页 4=落地页
     *
     * @see com.juzifenqi.plus.enums.CreateOrderSceneEnum
     */
    private Integer sceneCode;
}
