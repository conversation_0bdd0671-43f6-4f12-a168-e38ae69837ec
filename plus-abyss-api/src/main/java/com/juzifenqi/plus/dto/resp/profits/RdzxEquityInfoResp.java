package com.juzifenqi.plus.dto.resp.profits;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 融担咨询卡权益配置信息
 *
 * <AUTHOR>
 * @date 2024/6/18 上午11:34
 */
@Data
public class RdzxEquityInfoResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否有匹配权益
     */
    private Boolean isMatch;

    /**
     * 图片地址
     */
    private String imgUrl;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 市场价
     */
    private BigDecimal plusMarketPrice;

    /**
     * 还款返现金额信息
     */
    public List<HkfxAmountInfo> hkfxAmountInfos;

    /**
     * 还款返现金额信息
     */
    @Data
    public static class HkfxAmountInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 期数
         */
        private Integer periods;

        /**
         * 返现金额
         */
        private BigDecimal cashBackAmount;

    }

}
