package com.juzifenqi.plus.dto.resp.detail.land;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 方案权益信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13 18:13
 */
@Data
public class LandProfitDetailBasicResp implements Serializable {

    private static final long serialVersionUID = 3715029631114646340L;

    /**
     * 权益列表
     */
    private List<LandModelBasicDetailResp> modelBasicInfoVos;

    /**
     * 会员权益数量
     */
    private Integer rightsNum;

    /**
     * 虚拟权益列表
     */
    private List<LandVirtualProductTypeResp> programVirtualVos;
}
