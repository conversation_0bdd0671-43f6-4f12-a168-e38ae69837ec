package com.juzifenqi.plus.dto.resp.profits;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 权益0元发放虚拟商品列表返回
 *
 * <AUTHOR>
 * @date 2024/5/23 下午6:16
 */
@Data
public class PlusProgramLyffVirtualResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 商品ID(即权益ID,product表主键)
     */
    private Integer productId;

    /**
     * 商品名称(即权益名称)
     */
    private String productName;

    /**
     * 虚拟商品ID(virtual_goods表主键)
     */
    private Integer virtualGoodsId;

    /**
     * 商品sku
     */
    private String sku;

    /**
     * 会员折扣
     */
    private BigDecimal discountRate;

    /**
     * 售价
     */
    private BigDecimal sellPrice;

    /**
     * 会员价
     */
    private BigDecimal plusPrice;

    /**
     * 排序序号
     */
    private Integer rankNum;

    /**
     * 营销图片地址
     */
    private String imgUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人ID
     */
    private Integer updateUserId;

    /**
     * 修改人姓名
     */
    private String updateUserNm;

    /**
     * 最小金额
     */
    private BigDecimal minAmount;

    /**
     * 最大金额
     */
    private BigDecimal maxAmount;

}
