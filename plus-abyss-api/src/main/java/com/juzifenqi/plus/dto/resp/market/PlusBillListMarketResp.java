package com.juzifenqi.plus.dto.resp.market;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 账单列表页-还款卡营销返回
 *
 * <AUTHOR>
 * @date 2024/5/6 下午2:56
 */
@Data
public class PlusBillListMarketResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否可续费
     */
    private Integer canRenew;

    /**
     * 银行卡id
     */
    private Integer cardId;

    /**
     * 卡名称
     */
    private String bankName;

    /**
     * 卡号后四位
     */
    private String cardNo;

    /**
     * 银行卡图标
     */
    private String picture;

    /**
     * 收银台跳转url
     */
    private String bankUrl;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 还款卡AB测样式  1=A  2=B  默认1（A样式）
     */
    private Integer pageStyle = 1;

    /**
     * 还款券总金额：方案-开卡礼权益里的所有还款券总金额
     */
    private BigDecimal repayCouponAmount;


    /**
     * 是否支持后付款 1支持 2不支持
     */
    private Integer afterPayState;

    /**
     * 省钱文案
     */
    private String amountContent;

    /**
     * 后台方案名称
     */
    private String backstageName;

    /**
     * 是否设置了挽回弹窗，1-是，0-否
     */
    private Integer beSetRecovery;

    /**
     * 渠道
     */
    private Integer channel;

    /**
     * 会员类型id plus-config
     */
    private Integer configId;

    /**
     * 生效时间
     */
    private Date effectiveTime;

    /**
     * 首单首月优惠：0-关闭；1-开启
     */
    private int firstOrderDiscount;

    /**
     * 方案单价
     */
    private BigDecimal mallMobilePrice;

    /**
     * 营销文案
     */
    private String marketContent;

    /**
     * 划线价
     */
    private BigDecimal memberPrice;

    /**
     * 前台方案名称
     */
    private String name;

    /**
     * 方案天数
     */
    private int programmeDays;

    /**
     * 方案状态， 0 未生效 1 生效
     */
    private int programmeStatus;

    /**
     * 推荐标记 0 否  1 是
     */
    private int recommendedMarkup;

    /**
     * 挽回弹窗图片
     */
    private String recoveryImg;

    /**
     * 续费金额
     */
    private BigDecimal renewPrice;

    /**
     * 是否展示购买记录 0 否 1 是
     */
    private Integer showBuyRecord;

    /**
     * 是否展示到会员主页, 1-是，0-否
     */
    private Integer showHomePage;

    /**
     * 营销时间：最大2880分钟（48小时），用户认证失败48小时内展示失败卡会员
     */
    private Integer showTime;

    /**
     * 方案唯一标识
     */
    private String signProgram;

    /**
     * 状态，1-上架；2-下架
     */
    private int status;

    /**
     * 会员说明
     */
    private String userExplain;

    /**
     * 会员规则
     */
    private String userRules;

    /**
     * 支付方式
     */
    private List<Integer> payTypes;

    /**
     * 首付金额
     */
    private BigDecimal firstPayAmount;

    /**
     * 剩余首付支付金额
     */
    private BigDecimal surplusPayAmount;
}
