package com.juzifenqi.plus.dto.req.profits;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 新增权益0元发放配置入参
 *
 * <AUTHOR>
 * @date 2024/5/23 下午3:20
 */
@Data
public class CreateLyffVirtualGoodsReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会员方案ID
     */
    private Integer programId;

    /**
     * 会员类型模板ID
     */
    private Integer configId;

    /**
     * 操作人id
     */
    private Integer optId;

    /**
     * 操作者姓名
     */
    private String optName;

    /**
     * 虚拟商品列表
     */
    private List<LyffVirtualGoods> virtualGoodsList;

    @Data
    public static class LyffVirtualGoods implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 商品ID(即权益ID,product表主键)
         */
        private Integer productId;

        /**
         * 商品名称(即权益名称)
         */
        private String productName;

        /**
         * 虚拟商品ID(virtual_goods表主键)
         */
        private Integer virtualGoodsId;

        /**
         * 商品sku
         */
        private String sku;

        /**
         * 会员折扣
         */
        private BigDecimal discountRate;

        /**
         * 售价
         */
        private BigDecimal sellPrice;

        /**
         * 排序序号
         */
        private Integer rankNum;

        /**
         * 营销图片地址
         */
        private String imgUrl;

        /**
         * 虚拟商品上下架 0 下架 1 上架
         */
        private Integer status;

        /**
         * 最小金额
         */
        private BigDecimal minAmount;

        /**
         * 最大金额
         */
        private BigDecimal maxAmount;

    }

}
