package com.juzifenqi.plus.dto.req.admin.common;

import java.io.Serializable;
import lombok.Data;

/**
 * 会员折扣条件
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/23 11:28
 */
@Data
public class CreatePlusDiscountConditionReq implements Serializable {

    private static final long serialVersionUID = 5295506691347117432L;

    /**
     * 条件字段 参考DiscountConditionFieldEnum
     */
    private String conditionField;

    /**
     * 条件key 参考DiscountConditionKeyEnum
     */
    private String conditionKey;

    /**
     * 条件值
     */
    private String conditionVal;
}
