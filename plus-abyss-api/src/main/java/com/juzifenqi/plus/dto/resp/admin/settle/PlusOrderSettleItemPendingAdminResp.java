package com.juzifenqi.plus.dto.resp.admin.settle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 待结算明细
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/6 14:41
 */
@Data
public class PlusOrderSettleItemPendingAdminResp implements Serializable {
    private static final long serialVersionUID = -8269521838915331005L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 会员单号
     */
    private String orderSn;

    /**
     * 原请求支付流水号'
     */
    private String oriApplySerialNo;

    /**
     * 退款成功时间
     */
    private Date refundSuccessTime;

    /**
     * 结算单主键id
     */
    private Long settleBillId;

    /**
     * 分流主体id
     */
    private Integer shuntSupplierId;

    /**
     * 清分主体id
     */
    private Integer separateSupplierId;

    /**
     * 清分方式 1_比例 2_固定金额
     */
    private Integer separateType;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 结算金额
     */
    private BigDecimal settleAmount;

    /**
     * 结算状态 1_待结算 2_结算中 3_结算成功 4_结算失败
     */
    private Integer settleState;

    /**
     * 支付回调时间
     */
    private Date payCallbackTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
