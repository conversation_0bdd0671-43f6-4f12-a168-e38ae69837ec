package com.juzifenqi.plus.dto.req.admin;

import com.juzifenqi.plus.dto.req.PageEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 自动划扣订单查询请求
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/15 16:12
 */
@Data
public class PlusRenewQueryReq extends PageEntity implements Serializable {

    private static final long  serialVersionUID = 517912424069845565L;

    /**
     * 用户ID
     */
    private Integer userId;
    /**
     * 渠道
     */
    private String channelId;
    /**
     * 会员类型
     */
    private Integer configId;
    /**
     * 0划扣失效 ，1有效, 2方案已下架导致失效
     */
    private Integer state;
    /**
     * 创建开始时间
     */
    private String    startTime;
    /**
     * 创建结束时间
     */
    private String    endTime;
    /**
     * 取消续费开始时间
     */
    private String    cancelRenewStartTime;
    /**
     * 取消续费结束时间
     */
    private String    cancelRenewEndTime;
}
