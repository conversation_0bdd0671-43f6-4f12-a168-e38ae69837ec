package com.juzifenqi.plus.dto.resp.detail.profit;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户会员数据详情
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/5 11:15
 */
@Data
public class ProfitPlusDetailInfoResp implements Serializable {

    private static final long serialVersionUID = -136654114996630000L;

    /**
     * 会员到期时间
     */
    private String endTime;

    /**
     * 专属权益到期时间
     */
    private String profitEndTime;

    /**
     * 后付款订单号
     */
    private String orderAfterPay;

    /**
     * 会员对应的单号
     */
    private String orderSn;

    /**
     * 会员身份时间（当前阶段开始时间）
     */
    private Date currentStartTime;

    /**
     * 会员身份时间（当前阶段结束时间）
     */
    private Date currentEndTime;
}
