package com.juzifenqi.plus.dto.req.admin;

import java.io.Serializable;
import lombok.Data;

/**
 * 取消还款卡续费请求
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/16 10:48
 */
@Data
public class CancelRepayRenewReq implements Serializable {
    private static final long serialVersionUID = -7397323960491741658L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 会员单号
     */
    private String orderSn;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channel;

    /**
     * 操作人ID，系统操作
     */
    private Integer operatingId;

    /**
     * 操作人
     */
    private String operatingName;

    /**
     * 会员方案ID
     */
    private Integer programId;
}
