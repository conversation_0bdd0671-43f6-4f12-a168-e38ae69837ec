package com.juzifenqi.plus.dto.req;

import java.io.Serializable;
import lombok.Data;

/**
 * 保存急速退款任务入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/26 09:59
 */
@Data
public class CreateRefundRecordReq implements Serializable {

    private static final long serialVersionUID = 7015138543985686326L;


    /**
     * 用户渠道
     */
    private Integer channelId;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 借款订单号
     */
    private String loanOrderSn;

    /**
     * 会员订单号
     */
    private String plusOrderSn;

    /**
     * 会员类型
     */
    private Integer configId;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 取消方式
     *
     * @see com.juzifenqi.plus.enums.PlusCancelTypeEnum
     */
    private Integer cancelType;

    /**
     * 会员急速/延迟退款开关配置code
     */
    private Integer switchCode;
}
