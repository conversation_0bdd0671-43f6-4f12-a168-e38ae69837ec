package com.juzifenqi.plus.dto.resp.admin;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 订单清分明细
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/6 17:08
 */
@Data
public class PlusOrderSeparateItemAdminResp implements Serializable {
    private static final long serialVersionUID = -8269521838915331005L;

    /**
     * 请求支付流水号
     */
    private String applySerialNo;

    /**
     * 主体类型 1_分流主体 2_清分主体
     */
    private Integer supplierType;

    /**
     * 主体id
     */
    private Integer supplierId;

    /**
     * 清分方式 1_比例 2_固定金额
     */
    private Integer separateType;

    /**
     * 清分比例
     */
    private BigDecimal separateRate;

    /**
     * 分账金额
     */
    private BigDecimal separateAmount;

    /**
     * 分账状态 1_分账处理中 2_分账成功 3_分账失败
     */
    private Integer separateState;
}
