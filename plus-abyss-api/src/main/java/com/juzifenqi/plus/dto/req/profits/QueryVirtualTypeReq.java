package com.juzifenqi.plus.dto.req.profits;

import com.juzifenqi.plus.dto.req.PageEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2024/2/26 15:32
 * @description
 */
@Data
public class QueryVirtualTypeReq extends PageEntity implements Serializable {

    private static final long serialVersionUID = -5805981638308119320L;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 权益类型id
     */
    private Integer modelId;

    /**
     * 生活权益分类id
     */
    private Integer profitTypeId;

}
