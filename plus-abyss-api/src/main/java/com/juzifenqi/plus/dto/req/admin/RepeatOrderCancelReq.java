package com.juzifenqi.plus.dto.req.admin;

import java.io.Serializable;
import lombok.Data;

/**
 * 重复支付取消订单入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/13 16:20
 */
@Data
public class RepeatOrderCancelReq implements Serializable {

    private static final long serialVersionUID = 517912424069845565L;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 操作人id
     */
    private Integer optId;

    /**
     * 操作人姓名
     */
    private String optName;
}
