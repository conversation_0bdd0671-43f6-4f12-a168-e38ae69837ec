package com.juzifenqi.plus.dto.req.admin;

import com.juzifenqi.plus.dto.req.PageEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 会员订单列表入参
 * @date 2024/2/2 15:16
 */
@Data
public class PlusOrderInfoQueryReq extends PageEntity implements Serializable {

    /**
     * 用户ID
     */
    private Integer userId;
    /**
     * 手机号
     */
    private String  mobile;
    /**
     * 渠道id
     */
    private Integer channelId;
    /**
     * 订单号
     */
    private String  orderSn;
    /**
     * 订单类型 1-开通2-续费3-升级
     */
    private Integer orderType;
    /**
     * 会员类型id
     */
    private Integer configId;
    /**
     * 方案ID
     */
    private Integer programId;
    /**
     * 方案名称
     */
    private String  programName;
    /**
     * 订单状态 1_待支付;2_支付成功;3_客服取消订单(交易关闭-客服);4_急速退款-取消会员(交易关闭-退货)5_过期未支付(交易关闭-系统)
     */
    private Integer orderState;
    /**
     * 支付方式 1-全款支付2-划扣3-后付款
     */
    private Integer payType;
    /**
     * 创建开始时间
     */
    private String  startCreateTime;
    /**
     * 创建结束时间
     */
    private String  endCreateTime;

    /**
     * 会员状态
     */
    private Integer plusState;

}
