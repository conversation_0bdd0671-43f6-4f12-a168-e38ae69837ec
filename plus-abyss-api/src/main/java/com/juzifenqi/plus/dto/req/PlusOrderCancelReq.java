package com.juzifenqi.plus.dto.req;

import com.juzifenqi.plus.enums.CancelReasonEnum;
import com.juzifenqi.plus.enums.PlusCancelTypeEnum;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 取消会员订单入参
 */
@Data
public class PlusOrderCancelReq implements Serializable {

    private static final long serialVersionUID = 4089125657532681800L;

    /**
     * 订单号
     */
    private String plusOrderSn;

    /**
     * 取消类型：有条件、无条件、急速、延迟、未过期、过期、按比例
     *
     * @see PlusCancelTypeEnum
     */
    private Integer cancelType;

    /**
     * 取消原因code
     *
     * @see CancelReasonEnum
     */
    private Integer cancelReason;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作人id
     */
    private Integer optUserId;

    /**
     * 操作人姓名
     */
    private String optUserName;

    /**
     * 退款比例
     */
    private BigDecimal refundRate;

    /**
     * 已用权益类型逗号分隔
     */
    private String useProfit;

    /**
     * 操作人类型 1- C端用户  2- B端内部人员
     */
    private Integer optUserType;
}
