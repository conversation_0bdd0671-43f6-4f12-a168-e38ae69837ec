package com.juzifenqi.plus.dto.req.profits;

import com.juzifenqi.plus.dto.req.admin.PlusProgramCashbackReq;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 返现
 *
 * <AUTHOR>
 * @createTime 2024/3/1 20:14
 * @description
 */
@Data
public class CreatePlusGwfxProgramReq implements Serializable {

    private static final long serialVersionUID = 7678954709362362251L;

    /**
     * 创建人ID
     */
    private Integer createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 配置id
     */
    private Integer configId;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 权益id
     */
    private Integer modelId;

    /**
     * 返现配置列表
     */
    private List<PlusProgramCashbackReq> cashbacks;
}
