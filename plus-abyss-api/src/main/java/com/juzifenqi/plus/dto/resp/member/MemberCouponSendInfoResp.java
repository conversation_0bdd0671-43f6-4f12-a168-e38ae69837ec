package com.juzifenqi.plus.dto.resp.member;

import java.io.Serializable;
import lombok.Data;

/**
 * 会员优惠券发放信息返回
 *
 * <AUTHOR>
 * @date 2024/8/15 10:33
 */
@Data
public class MemberCouponSendInfoResp implements Serializable {

    private static final long serialVersionUID = 5847073519550390250L;

    /**
     * 用户优惠券唯一标识
     */
    private Integer couponUserId;

    /**
     * 用户还款券唯一标识
     */
    private String couponNo;

    /**
     * 优惠券id
     */
    private Integer couponId;

    /**
     * 优惠券会员标识
     */
    private boolean vipMark;

    /**
     * vip标识 1-会员发放打标 2-会员身份打标
     */
    private Integer couponVipFlag;

    /**
     * 优惠券发券时间
     */
    private String sendCouponTime;

}
