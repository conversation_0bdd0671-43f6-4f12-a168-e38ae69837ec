package com.juzifenqi.plus.dto.req.profits;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 区间还款配置优惠券-编辑
 *
 * <AUTHOR>
 * @date 2024/7/31 上午10:02
 */
@Data
public class EditQjhkCouponConfigReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 减免金额最小金额
     */
    private BigDecimal minAmount;

    /**
     * 减免金额最大金额
     */
    private BigDecimal maxAmount;

    /**
     * 期数
     */
    private Integer periodNum;

    /**
     * 优惠券id
     */
    private Integer couponId;

    /**
     * 操作人id
     */
    private Integer optId;

    /**
     * 操作者姓名
     */
    private String optName;

}
