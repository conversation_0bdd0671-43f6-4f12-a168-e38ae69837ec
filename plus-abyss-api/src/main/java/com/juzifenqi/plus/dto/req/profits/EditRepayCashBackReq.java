package com.juzifenqi.plus.dto.req.profits;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 编辑还款返现配置入参
 *
 * <AUTHOR>
 * @date 2024/6/11 下午4:25
 */
@Data
public class EditRepayCashBackReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 借款最小金额
     */
    private BigDecimal minAmount;

    /**
     * 借款最大金额
     */
    private BigDecimal maxAmount;

    /**
     * 借款期数
     */
    private Integer periods;

    /**
     * 返现金额
     */
    private BigDecimal cashbackAmount;

    /**
     * 首次返现比例
     */
    private BigDecimal firstScale;

    /**
     * 操作人id
     */
    private Integer optId;

    /**
     * 操作者姓名
     */
    private String optName;

}
