package com.juzifenqi.plus.dto.resp.admin.settle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 结算单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/9 17:05
 */
@Data
public class SettleBillResp implements Serializable {
    private static final long serialVersionUID = -8269521838915331005L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 待结算日期
     */
    private Date settlePendingDate;

    /**
     * 结算单号
     */
    private String settleBillNo;

    /**
     * 分流主体id
     */
    private Integer shuntSupplierId;

    /**
     * 清分主体id
     */
    private Integer separateSupplierId;

    /**
     * 结算金额
     */
    private BigDecimal settleAmount;

    /**
     * 申请结算时间
     */
    private Date applySettleTime;

    /**
     * 结算状态 1_待结算 2_结算中 3_结算成功 4_结算失败
     */
    private Integer settleState;

    /**
     * 支付回调时间
     */
    private Date payCallbackTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
