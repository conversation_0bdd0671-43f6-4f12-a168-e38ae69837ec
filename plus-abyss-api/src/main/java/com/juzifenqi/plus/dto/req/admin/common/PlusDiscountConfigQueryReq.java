package com.juzifenqi.plus.dto.req.admin.common;

import com.juzifenqi.plus.dto.req.PageEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 会员折扣列表查询入参
 *
 * <AUTHOR>
 * @date 2022/9/06 10:33
 */
@Data
public class PlusDiscountConfigQueryReq extends PageEntity implements Serializable {


    private static final long serialVersionUID = 1232768238566474418L;

    /**
     * 客群名称
     */
    private String confName;

    /**
     * 折扣类型 1=客群折扣 2=会员折扣
     */
    private Integer confType;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 状态
     */
    private Integer confState;
}