package com.juzifenqi.plus.dto.resp.admin.shunt;

import com.juzifenqi.plus.enums.supplier.SeparateTypeEnum;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 分流主体清分配置返回
 *
 * <AUTHOR>
 * @date 2024/8/29 11:14
 */
@Data
public class PlusShuntSupplierSeparateResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 业务场景/会员收款方式
     */
    private String businessScene;

    /**
     * 结算主体类型 2-清分 3-默认清分
     *
     * @see com.juzifenqi.plus.enums.supplier.SettleSupplierTypeEnum
     */
    private Integer supplierType;
    /**
     * 清分方式
     *
     * @see SeparateTypeEnum
     */
    private Integer separateType;

    /**
     * 清分主体id
     */
    private Integer separateSupplierId;

    /**
     * 清分比例
     */
    private BigDecimal separateRate;

    /**
     * 清分金额
     */
    private BigDecimal separateAmount;

}
