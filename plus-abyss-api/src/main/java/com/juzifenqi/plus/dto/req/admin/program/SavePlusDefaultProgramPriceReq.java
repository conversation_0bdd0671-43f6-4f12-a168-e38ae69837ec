package com.juzifenqi.plus.dto.req.admin.program;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 默认方案价
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/13 10:50
 */
@Data
public class SavePlusDefaultProgramPriceReq implements Serializable {

    private static final long serialVersionUID = 5779592662219839409L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 主表id
     */
    private Integer priceId;

    /**
     * 默认方案id
     */
    private Integer programId;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private Date createTime;
}
