package com.juzifenqi.plus.dto.resp.profits;

import com.juzifenqi.plus.enums.RdSendTypeEnum;
import java.io.Serializable;
import lombok.Data;

/**
 * 融担咨询卡权益配置信息返回
 *
 * <AUTHOR>
 * @date 2024/6/13 下午5:30
 */
@Data
public class RdzxEquityConfigInfoResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开启权益发放类型
     *
     * @see RdSendTypeEnum
     */
    private Integer rdSendType;

    /**
     * 挽留弹窗是否开启 0-关闭 1-开启
     */
    private Integer popUp;

    /**
     * 二次挽留弹窗是否开启 0-关闭 1-开启
     */
    private Integer twoPopUp;

    /**
     * 关闭挽留弹窗是否选中融担卡 0-否 1-是
     */
    private Integer rdChoose;

    /**
     * 权益配置信息
     */
    private RdzxEquityInfoResp equityInfo;

}
