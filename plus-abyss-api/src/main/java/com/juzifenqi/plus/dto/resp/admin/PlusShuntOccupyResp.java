package com.juzifenqi.plus.dto.resp.admin;

import java.io.Serializable;
import lombok.Data;

/**
 * 会员分流当天额度和笔数占用信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/3 17:10
 */
@Data
public class PlusShuntOccupyResp implements Serializable {

    private static final long serialVersionUID = -5297372595865929155L;

    /**
     * 分流方id
     */
    private Integer supplierId;

    /**
     * 分流方名称
     */
    private String supplierName;

    /**
     * 计划占用总金额
     */
    private String planAmount = "0";

    /**
     * 计划占用总笔数
     */
    private String planCount = "0";

    /**
     * 实际占用总金额
     */
    private String amount = "0";

    /**
     * 实际占用总笔数
     */
    private String count = "0";
}
