package com.juzifenqi.plus.dto.resp.detail.land;

import java.io.Serializable;
import java.util.Map;
import lombok.Data;

/**
 * 权益基本信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13 18:08
 */
@Data
public class LandModelBasicDetailResp implements Serializable {

    private static final long serialVersionUID = 7451024812868897301L;

    private int id;

    /**
     * 方案ID
     */
    private int programId;

    /**
     * 会员权益ID
     */
    private int modelId;

    /**
     * 方案名称
     */
    private String name;

    /**
     * 权益简称
     */
    private String shortName;

    /**
     * 引导文案
     */
    private String guideCopy;

    /**
     * 省钱文案
     */
    private String savingCopy;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 拼音简称
     */
    private String shortPY;

    /**
     * 规则说明
     */
    private String ruleExplain;

    /**
     * 规则配图
     */
    private String ruleImage;

    /**
     * 其它属性信息
     */
    private Map<String, Object> data;

    /**
     * 权益弹窗展示状态:1：展示 0不展示'
     */
    private Integer alertState;
}
