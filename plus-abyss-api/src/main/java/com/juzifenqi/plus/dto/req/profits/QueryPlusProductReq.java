package com.juzifenqi.plus.dto.req.profits;

import com.juzifenqi.plus.dto.req.PageEntity;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2024/2/29 19:14
 * @description
 */
@Data
public class QueryPlusProductReq extends PageEntity {

    private static final long serialVersionUID = 6316535729648716939L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品id
     */
    private Integer productId;

    /**
     * 商品sku
     */
    private String productSku;

    /**
     * 采购价
     */
    private BigDecimal purPrice;

    /**
     * 扣减保护价
     */
    private BigDecimal protPrice;

    /**
     * 0-未上架 1-已上架
     */
    private Integer onSaleState;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 修改人id
     */
    private String updateUserId;

    /**
     * 修改人姓名
     */
    private String updateUserName;
}
