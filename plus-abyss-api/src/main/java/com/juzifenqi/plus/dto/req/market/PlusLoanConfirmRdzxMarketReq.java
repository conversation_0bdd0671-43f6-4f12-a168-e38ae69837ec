package com.juzifenqi.plus.dto.req.market;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 确认借款页-融担卡营销入参
 *
 * <AUTHOR>
 * @date 2024/5/8 上午9:58
 */
@Data
public class PlusLoanConfirmRdzxMarketReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道
     */
    private Integer channelId;

    /**
     * 借款金额
     */
    private BigDecimal loanAmount;

    /**
     * 风控差异化利率数组“2:0.24,4:0.36”
     */
    private String loanRate;

    /**
     * 资方id
     */
    private Integer capitalId;

    /**
     * 渠道标识
     */
    private Integer bizSource;

}
