package com.juzifenqi.plus.dto.resp.detail.profit;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 会员合并-用户权益页数据展示
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/06/12 11:15
 */
@Data
public class ProfitMergeDetailResp implements Serializable {

    private static final long serialVersionUID = -2854224491031419183L;


    /**
     * 用户权益页数据展示-公共数据
     */
    private ProfitMergeCommonDetailResp commonInfo;

    /**
     * 用户权益页数据展示-可能对应多个会员订单
     */
    private List<ProfitDetailResp> profitInfos;
}
