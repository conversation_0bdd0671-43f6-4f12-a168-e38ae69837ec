package com.juzifenqi.plus.dto.req;

import java.io.Serializable;
import lombok.Data;

/**
 * 急速退款任务执行入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/27 09:45
 */
@Data
public class RefundExecuteReq implements Serializable {

    private static final long serialVersionUID = -8835314352678254729L;

    /**
     * 每次任务执行数量。默认30个
     */
    private Integer size = 30;

    /**
     * 处理状态，区分重试和正常执行的job
     *
     * @see PlusRefundStatusEnum
     */
    private Integer optState;

    /**
     * 重试标识
     */
    private boolean retry;
}
