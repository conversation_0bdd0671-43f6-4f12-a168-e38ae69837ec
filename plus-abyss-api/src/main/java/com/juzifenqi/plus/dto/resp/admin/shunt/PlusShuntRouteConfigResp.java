package com.juzifenqi.plus.dto.resp.admin.shunt;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 分流配置详情
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/20 10:23
 */
@Data
public class PlusShuntRouteConfigResp implements Serializable {

    private static final long serialVersionUID = -7261502099175803801L;

    /**
     * 开关
     */
    private Integer switchState;

    /**
     * 兜底主体id
     */
    private Integer supplierId;

    /**
     * 路由列表
     */
    private List<PlusShuntRouteResp> routeList;
}
