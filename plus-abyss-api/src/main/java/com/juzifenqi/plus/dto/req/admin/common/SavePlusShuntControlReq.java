package com.juzifenqi.plus.dto.req.admin.common;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 保存分类配置入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/5 16:24
 */
@Data
public class SavePlusShuntControlReq implements Serializable {

    private static final long serialVersionUID = -7479309727286950116L;

    /**
     * 主键（编辑时用到）
     */
    private Integer id;

    /**
     * 开关状态 1_关闭 2_开启
     */
    private Integer switchState;

    /**
     * 订单金额最小值
     */
    private BigDecimal minOrderAmount;

    /**
     * 订单金额最大值
     */
    private BigDecimal maxOrderAmount;

    /**
     * 订单总金额
     */
    private BigDecimal orderAmount;

    /**
     * 订单总笔数
     */
    private Integer orderCount;

    /**
     * 桔子分流比例
     */
    private Integer juziRate;

    /**
     * 黑卡分流比例
     */
    private Integer oakRate;

    /**
     * 操作人id
     */
    private String optUserId;

    /**
     * 操作人名称
     */
    private String optUserName;
}
