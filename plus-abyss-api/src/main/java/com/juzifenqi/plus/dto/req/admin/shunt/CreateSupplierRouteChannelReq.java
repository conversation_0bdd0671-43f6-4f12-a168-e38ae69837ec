package com.juzifenqi.plus.dto.req.admin.shunt;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/26  19:46
 * @description 创建分流路由配置渠道入参
 */
@Data
public class CreateSupplierRouteChannelReq implements Serializable {

    private static final long serialVersionUID = 4430595416160306413L;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 是否重提用户 1-否 2-是
     */
    private Integer resubmitUser;

    /**
     * 最小订单金额
     */
    private BigDecimal minOrderAmount;

    /**
     * 最大订单金额
     */
    private BigDecimal maxOrderAmount;

    /**
     * 业务场景信息
     */
    private List<CreateSupplierRouteChannelBusinessSceneReq> businessSceneList;
}
