package com.juzifenqi.plus.dto.req;

import java.io.Serializable;
import lombok.Data;

/**
 * 保存订单回调任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/23 17:36
 */
@Data
public class CreateOrderNoticeTaskReq implements Serializable {

    private static final long serialVersionUID = 4800438315787836943L;

    /**
     * 会员单号
     */
    private String orderSn;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 通知类型 1_开通会员 2_退卡结果
     */
    private Integer noticeType;

    /**
     * 业务参数
     */
    private String busParam;
}
