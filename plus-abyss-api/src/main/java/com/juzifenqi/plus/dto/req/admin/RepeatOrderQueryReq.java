package com.juzifenqi.plus.dto.req.admin;

import com.juzifenqi.plus.dto.req.PageEntity;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 会员重复支付查询请求
 *
 * @Author:gaoyu
 * @Date: 2022-05-10 10:31:17
 * @Description:
 */
@Data
public class RepeatOrderQueryReq extends PageEntity implements Serializable {

    private static final long  serialVersionUID = 517912424069845565L;
    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 渠道
     */
    private Integer channelId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 会员类别id
     */
    private Integer configId;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 状态：0待处理 1已处理
     */
    private String optState;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

}
