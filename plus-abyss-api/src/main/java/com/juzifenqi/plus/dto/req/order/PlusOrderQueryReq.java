package com.juzifenqi.plus.dto.req.order;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 订单查询条件
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/14 14:45
 */
@Data
public class PlusOrderQueryReq implements Serializable {

    private static final long serialVersionUID = 2719827785983263364L;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 支付方式 1-全款支付2-划扣3-后付款
     */
    private Integer payType;

    /**
     * 订单状态 1_待支付;2_支付成功;3_取消
     */
    private Integer orderState;

    private List<Integer> payTypes;
}
