package com.juzifenqi.plus.dto.req.profits;

import com.juzifenqi.plus.dto.req.PageEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 查询方案权益下的优惠券-后台
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/9 10:19
 */
@Data
public class QueryProgramCouponReq extends PageEntity implements Serializable {

    private static final long serialVersionUID = -5805981638308119320L;

    /**
     * 方案ID
     */
    private Integer programId;
    /**
     * 拒就陪类型：1、审核不通过；2、认证不通过
     */
    private Integer type;

    /**
     * 权益类型id
     */
    private Integer modelId;

    /**
     * 下单笔数
     */
    private int ordersNumber;

    /**
     * 订单满足金额
     */
    private BigDecimal orderPrice;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 优惠券唯一索引标识
     */
    private Integer index;

    /**
     * 优惠券ID
     */
    private Integer couponId;

    /**
     * 优惠券类别：0-满减优惠券，1-免息优惠券，2、折扣优惠券，3、还款优惠券，4、息费折扣券
     */
    private Integer couponCategory;
}
