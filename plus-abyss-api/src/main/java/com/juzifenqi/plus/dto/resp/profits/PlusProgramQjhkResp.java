package com.juzifenqi.plus.dto.resp.profits;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 区间还款优惠折扣配置返回
 * @date 2024/4/2 15:04
 */
@Data
public class PlusProgramQjhkResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 折扣配置
     */
    private BigDecimal discountRate;

    /**
     * 修改时间
     */
    private Date updateTime;

}
