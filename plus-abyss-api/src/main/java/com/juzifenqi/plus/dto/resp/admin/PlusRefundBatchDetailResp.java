package com.juzifenqi.plus.dto.resp.admin;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 批量取消详情返回结构
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/28 10:31
 */
@Data
public class PlusRefundBatchDetailResp implements Serializable {

    private static final long serialVersionUID = 8286619611235136912L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道
     */
    private Integer channelId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 会员类型
     */
    private Integer configId;

    /**
     * 状态: 0_待处理 1_处理中 2_处理成功 3_处理失败
     */
    private Integer optStatus;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 备注
     */
    private String remark;

    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}