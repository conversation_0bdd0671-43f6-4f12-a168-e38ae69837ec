package com.juzifenqi.plus.dto.resp.admin.program;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 默认方案价
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/13 10:50
 */
@Data
public class PlusDefaultProgramPriceResp implements Serializable {

    private static final long serialVersionUID = -4449147013336006949L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 主表id
     */
    private Integer priceId;

    /**
     * 默认方案id
     */
    private Integer programId;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private Date createTime;
}
