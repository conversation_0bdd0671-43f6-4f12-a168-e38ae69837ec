package com.juzifenqi.plus.dto.req.admin.common;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 保存会员黑名单入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/3 17:39
 */
@Data
public class CreatePlusBlackReq implements Serializable {

    private static final long serialVersionUID = 6454685745018162708L;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 操作人ID
     */
    private Integer optUserId;

    /**
     * 操作姓名
     */
    private String optUserName;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 批量操作的用户id
     */
    private List<Integer> userIds;

    /**
     * 黑名单类型 1-后付款  2-会员黑名单
     */
    private Integer blackType;
}
