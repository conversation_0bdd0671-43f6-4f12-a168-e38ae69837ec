package com.juzifenqi.plus.dto.resp.admin.common;

import java.io.Serializable;
import lombok.Data;

/**
 * 短信参数
 */
@Data
public class PlusSmsParamResp implements Serializable {

    private static final long serialVersionUID = 8460180873778586800L;

    /**
     * 排序-用于给短信服务传参时，对短信的模板字段入参排序
     */
    private Integer sort;

    /**
     * 参数key
     */
    private String paramKey;

    /**
     * 参数值-用于策略获取各个变量值
     */
    private String paramValue;

    /**
     * 是否存在于可配置参数中，默认存在
     * <p>用于用户参数不符查看详情时</p>
     */
    private boolean existParam = true;

    /**
     * 参数名称
     * <p>用于用户参数不符查看详情时</p>
     */
    private String paramName;
}
