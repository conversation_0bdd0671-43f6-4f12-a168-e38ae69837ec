package com.juzifenqi.plus.dto.req.market;

import java.io.Serializable;
import lombok.Data;

/**
 * 借款首页营销入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/11 16:03
 */
@Data
public class PlusLoanMarketReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 创单场景入口标识  1=借款首页 2=确认借款页 3=信用支付完成页 4=落地页
     */
    private Integer sceneCode;

    /**
     * 渠道标识 1-ykd  2-桔多多
     */
    private Integer bizSource;
}
