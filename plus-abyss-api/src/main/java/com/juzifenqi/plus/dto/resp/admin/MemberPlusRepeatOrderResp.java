package com.juzifenqi.plus.dto.resp.admin;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 重复支付订单返回详情
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/13 16:24
 */
@Data
public class MemberPlusRepeatOrderResp implements Serializable {

    private static final long serialVersionUID = 8286619611235136912L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 渠道
     */
    private Integer channelId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 会员类别id
     */
    private Integer configId;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 会员卡名
     */
    private String programName;

    /**
     * 状态：0待处理 1已处理
     */
    private Integer optState;

    /**
     * 支付方式 1-全款支付2-划扣3-后付款
     */
    private Integer payType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * bi时间
     */
    private Date biTime;

    /**
     * 渠道标识 1-宜口袋 2-桔多多
     */
    private Integer bizSource;
}