package com.juzifenqi.plus.dto.req;

import java.io.Serializable;
import lombok.Data;

/**
 * 订单通知执行
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/24 10:23
 */
@Data
public class OrderNoticeExecuteReq implements Serializable {

    private static final long serialVersionUID = -5386439822469580657L;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 通知类型 1_开通会员 2_退卡结果
     */
    private Integer noticeType;

    /**
     * 通知状态 1_待通知 2_通知中 3_通知成功 4_通知失败
     */
    private Integer noticeState;

    /**
     * 每次执行数量
     */
    private Integer size;

    /**
     * 是否重试执行
     */
    private boolean retry;
}
