package com.juzifenqi.plus.dto.resp.member;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 会员卡类型返回
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/4 11:14
 */
@Data
public class MemberPlusConfigResp implements Serializable {

    private static final long serialVersionUID = 5847073519550390250L;

    /**
     * 是否是会员 0 不是会员  1 是会员
     */
    private Integer isPlus;

    /**
     * 会员类型 例：J,F,SU,RC
     */
    private String plusCodeBuf;

    /**
     * 已购买的会员合集
     */
    private List<MemberPlusConfigDetailResp> plusList;
}
