package com.juzifenqi.plus.dto.resp.market;

import com.juzifenqi.plus.dto.resp.admin.PlusModelResp;
import com.juzifenqi.plus.dto.resp.admin.shunt.PlusShuntSupplierContractResp;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 订单列表页-加速卡营销返回
 *
 * <AUTHOR>
 * @date 2024/5/6 上午11:05
 */
@Data
public class PlusOrderListMarketResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 展示逻辑
     */
    private Integer showStatus;
    /**
     * 跳转地址
     */
    private String  skipUrl;

    /**
     * 落地页地址
     */
    private String  landingPageUrl;

    /**
     * 加速卡营销图片
     */
    private String  entranceImg;

    /**
     * 会员类型
     */
    private Integer  configId;

    /**
     * 方案id
     */
    private Integer  programId;

    /**
     * 分流合作方id
     * <p>营销卡的前提下才可能返回</p>
     */
    private Integer shuntSupplierId;

    /**
     * 样式预留字段
     */
    private String styleCode;

    /**
     * 分流主体名称
     */
    private String supplierName;

    /**
     * 分流主体合同列表
     */
    private List<PlusShuntSupplierContractResp> contractList;

    /**
     * 营销方案名称
     */
    private String programName;

    /**
     * 是否支持后付款 1支持 2不支持
     */
    private Integer afterPayState;

    /**
     * 营销方案价格
     */
    private BigDecimal mallMobilePrice;

    /**
     * 是否符合折扣 默认不符合 = 0  符合：1
     */
    private Integer showDiscountInfo = 0;

    /**
     * 折扣
     */
    private BigDecimal discountRate;

    /**
     * 折扣价格
     */
    private BigDecimal discountPrice;

    /**
     * 风控可提额度
     * <p>风控返回</p>
     */
    private BigDecimal raiseAmount;

    /**
     * 会员权益列表
     */
    private List<PlusModelResp> plusRightsList;

    /**
     * 支付方式
     */
    private List<Integer> payTypes;

    /**
     * 首付金额
     */
    private BigDecimal firstPayAmount;

    /**
     * 剩余首付支付金额
     */
    private BigDecimal surplusPayAmount;
}
