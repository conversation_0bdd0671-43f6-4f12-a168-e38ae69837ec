package com.juzifenqi.plus.dto.resp.market;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 导流页-桔省卡营销返回
 *
 * <AUTHOR>
 * @date 2024/5/8 下午2:39
 */
@Data
public class PlusLeadPageMarketResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 展示类型, 0-不展示；14-桔省卡
     */
    private Integer showType;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 会员价格
     */
    private BigDecimal mallMobilePrice;

}
