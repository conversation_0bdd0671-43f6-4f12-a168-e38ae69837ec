package com.juzifenqi.plus.dto.req.profits;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 区间还款配置优惠券-修改状态
 *
 * <AUTHOR>
 * @date 2024/7/31 上午10:02
 */
@Data
public class EditQjhkCouponConfigStateReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 状态 1-启用 2-停用
     */
    private Integer state;

    /**
     * 操作人id
     */
    private Integer optId;

    /**
     * 操作者姓名
     */
    private String optName;

}
