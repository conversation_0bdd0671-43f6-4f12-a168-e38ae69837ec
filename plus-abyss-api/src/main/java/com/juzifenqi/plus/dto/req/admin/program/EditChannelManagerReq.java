package com.juzifenqi.plus.dto.req.admin.program;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 创建渠道管理入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/5 19:50
 */
@Data
public class EditChannelManagerReq implements Serializable {

    private static final long serialVersionUID = -9100468764141676486L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 渠道营销会员类型id，以逗号分割
     */
    private String plusConfigIds;

    /**
     * 渠道功能
     */
    private List<CreateChannelFunctionReq> plusChannelFunctions;

    /**
     * 操作人id
     */
    private Integer optUserId;

    /**
     * 操作人名称
     */
    private String optUserName;
}
