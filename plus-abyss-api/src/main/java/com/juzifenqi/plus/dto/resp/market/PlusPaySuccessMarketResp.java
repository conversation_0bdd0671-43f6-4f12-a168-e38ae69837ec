package com.juzifenqi.plus.dto.resp.market;

import com.juzifenqi.plus.dto.resp.admin.PlusModelResp;
import com.juzifenqi.plus.dto.resp.admin.shunt.PlusShuntSupplierContractResp;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 信用支付成功页会员营销返回结构
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/22 17:08
 */
@Data
public class PlusPaySuccessMarketResp implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 0=不营销任何卡 12=小额月卡  3=加速卡，此字段含义与借款首页、确认借款页此字段含义一致
     */
    private Integer showType;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 营销的方案id
     */
    private Integer programId;

    /**
     * 分流合作方id
     * <p>营销卡的前提下才可能返回</p>
     */
    private Integer shuntSupplierId;

    /**
     * 方案价格
     */
    private BigDecimal mallMobilePrice;

    /**
     * 分流主体名称
     */
    private String supplierName;

    /**
     * 分流主体合同列表
     */
    private List<PlusShuntSupplierContractResp> contractList;

    /**
     * 样式预留字段
     */
    private String styleCode;

    /**
     * 会员权益列表
     */
    private List<PlusModelResp> plusRightsList;

    /**
     * 是否支持后付款 1支持 2不支持
     */
    private Integer afterPayState;

    /**
     * 支付方式
     */
    private List<Integer> payTypes;

    /**
     * 首付金额
     */
    private BigDecimal firstPayAmount;

    /**
     * 剩余首付支付金额
     */
    private BigDecimal surplusPayAmount;

}
