package com.juzifenqi.plus.dto.req;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PlusOrderRelationCreateReq implements Serializable {

    private static final long   serialVersionUID = -1L;
    /**
     * 会员订单号
     */
    private              String plusOrderSn;
    /**
     * 业务订单号(必传)
     */
    private              String orderSn;

    /**
     * 业务订单创建时间
     */
    private Date orderCreateTime;

    /**
     * 会员类型（必传）
     */
    private Integer configId;

    /**
     * 业务类型：详见 PlusOrderRelationBusinessType（必传）
     */
    private Integer businessType;

    /**
     * 用户id
     */
    private Integer userId;
    /**
     * 渠道id
     */
    private Integer channelId;
}
