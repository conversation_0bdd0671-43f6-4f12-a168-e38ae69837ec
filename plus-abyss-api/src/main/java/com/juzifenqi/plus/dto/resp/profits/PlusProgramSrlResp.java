package com.juzifenqi.plus.dto.resp.profits;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 生日礼
 *
 * <AUTHOR>
 * @createTime 2024/2/28 17:28
 * @description
 */
@Data
public class PlusProgramSrlResp implements Serializable {

    private static final long serialVersionUID = -7037906194787207800L;


    /**
     * 主键
     */
    private Integer id;

    /**
     * 会员类型ID
     */
    private Integer configId;

    /**
     * 会员方案ID
     */
    private Integer programId;

    /**
     * 广告图url
     */
    private String imgUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建人ID
     */
    private Integer createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 修改人ID
     */
    private Integer updateUserId;

    /**
     * 修改人姓名
     */
    private String updateUserName;
}
