package com.juzifenqi.plus.dto.req.admin.program;

import com.juzifenqi.plus.dto.req.PageEntity;
import lombok.Data;

/**
 * 会员定价列表参查询数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/12 16:43
 */
@Data
public class PriceQueryReq extends PageEntity {

    private static final long serialVersionUID = -5719290907773296612L;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 定价类型 1=默认方案价 2=差异化定价
     */
    private Integer priceType;
}
