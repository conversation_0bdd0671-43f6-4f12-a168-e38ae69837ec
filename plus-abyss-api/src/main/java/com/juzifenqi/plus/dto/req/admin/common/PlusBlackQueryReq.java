package com.juzifenqi.plus.dto.req.admin.common;

import com.juzifenqi.plus.dto.req.PageEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 黑名单列表查询入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/4 14:29
 */
@Data
public class PlusBlackQueryReq extends PageEntity implements Serializable {

    private static final long serialVersionUID = -2106706887923233116L;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 黑名单类型
     */
    private Integer blackType;
}
