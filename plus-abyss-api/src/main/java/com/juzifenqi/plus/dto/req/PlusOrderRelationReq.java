package com.juzifenqi.plus.dto.req;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 获取订单关联信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/16 10:16
 */
@Data
public class PlusOrderRelationReq implements Serializable {

    private static final long serialVersionUID = -828495252423764580L;

    /**
     * 订单号，依据orderType传入不同订单号
     */
    private List<String> orderSns;

    /**
     * 订单类型,1=会员单 2=借款单
     */
    private Integer orderType;

    /**
     * 关联业务类型
     */
    private Integer businessType;
}
