package com.juzifenqi.plus.dto.req.profits;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 会员商品池
 *
 * <AUTHOR>
 * @createTime 2024/2/29 19:13
 * @description
 */
@Data
public class CreatePlusProductReq implements Serializable {

    private static final long serialVersionUID = 5252105172643465421L;

    /**
     * id
     */
    private Integer id;

    /**
     * 商品id
     */
    private Integer productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品SKU
     */
    private String productSku;

    /**
     * 会员商品上架状态 0_未上架 1_已上架
     */
    private Integer onSaleState;

    /**
     * 采购价
     */
    private BigDecimal purPrice;

    /**
     * 扣减保护价
     */
    private BigDecimal protPrice;

    /**
     * 商品库存
     */
    private Integer stock;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人id
     */
    private String updateUserId;

    /**
     * 修改人名称
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private Date updateTime;
}
