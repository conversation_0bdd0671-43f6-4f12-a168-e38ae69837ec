package com.juzifenqi.plus.dto.req.profits;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 发放权益mq内容
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/15 14:44
 */
@Data
public class SendProfitsMqReq implements Serializable {

    private static final long serialVersionUID = 218953258355036356L;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 会员单号
     */
    private String plusOrderSn;

    /**
     * 借款单号
     */
    private String orderSn;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 已生成发放计划的权益id
     */
    private List<Integer> sendPlanModelIds;
}
