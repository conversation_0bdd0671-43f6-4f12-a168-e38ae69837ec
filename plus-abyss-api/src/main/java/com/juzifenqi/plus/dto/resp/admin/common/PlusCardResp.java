package com.juzifenqi.plus.dto.resp.admin.common;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 会员卡片配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/5 16:47
 */
@Data
public class PlusCardResp implements Serializable {

    private static final long serialVersionUID = -6286460879327305826L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 是否展示1是;0否
     */
    private Integer isShow;

    /**
     * 卡片内容
     */
    private String cardContent;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人ID
     */
    private Integer createUserId;

    /**
     * 创建人姓名
     */
    private String createUser;

    /**
     * 编辑时间
     */
    private Date updateTime;

    /**
     * 编辑人ID
     */
    private Integer updateUserId;

    /**
     * 编辑人姓名
     */
    private String updateUser;
}