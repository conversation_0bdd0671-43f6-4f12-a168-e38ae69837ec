package com.juzifenqi.plus.dto.req.admin;

import com.juzifenqi.plus.dto.req.PageEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 对账记录查询入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/18 14:50
 */
@Data
public class OrderBillQueryReq extends PageEntity implements Serializable {

    private static final long serialVersionUID = 3171081921913774629L;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 会员单号
     */
    private String plusOrderSn;

    /**
     * 会员订单状态
     */
    private Integer plusOrderState;

    /**
     * 入账方
     */
    private Integer inSupplierId;

    /**
     * 出账方
     */
    private Integer outSupplierId;

    /**
     * 入账成功开始时间
     */
    private String inStartTime;

    /**
     * 入账成功开始时间
     */
    private String inEndTime;
}
