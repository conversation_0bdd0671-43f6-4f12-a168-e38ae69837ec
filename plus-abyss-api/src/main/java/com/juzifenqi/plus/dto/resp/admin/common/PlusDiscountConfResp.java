package com.juzifenqi.plus.dto.resp.admin.common;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 会员折扣配置表Vo
 *
 * <AUTHOR>
 * @date 2022/9/06 10:33
 */
@Data
public class PlusDiscountConfResp implements Serializable {

    private static final long serialVersionUID = 390898224850158103L;

    /**
     * 配置id
     */
    private Integer id;

    /**
     * 客群名称
     */
    private String confName;

    /**
     * 客群code
     */
    private String confCode;

    /**
     * 折扣类型 1=客群折扣 2=会员折扣
     */
    private Integer confType;

    /**
     * 标签 1=普通客群 2=重提订单客群
     */
    private Integer confTag;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 折扣
     */
    private BigDecimal discountRate;

    /**
     * 时效（单位：小时）
     */
    private Integer effectiveTime;

    /**
     * 状态 0-停用 1-启用
     */
    private Integer confState;

    /**
     * 操作人ID
     */
    private Integer optUserId;

    /**
     * 操作姓名
     */
    private String optUserName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 折扣条件
     */
    private List<PlusDiscountConditionResp> conditionList;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}