package com.juzifenqi.plus.dto.req.profits;

import com.juzifenqi.plus.dto.req.admin.PlusLiftAmountReq;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 方案与权益类型的关联信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/8 15:14
 */
@Data
public class CreatePlusProModelReq implements Serializable {

    private static final long serialVersionUID = 6771275521742484316L;

    /**
     * id
     */
    private Integer id;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 会员权益ID
     */
    private Integer modelId;

    /**
     * 备注  model_id:5(权益套餐id)
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date   updateTime;
    /**
     * 方案名称
     */
    private String name;

    /**
     * 权益简称
     */
    private String shortName;

    /**
     * 引导文案
     */
    private String guideCopy;

    /**
     * 省钱文案
     */
    private String savingCopy;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 规则说明
     */
    private String ruleExplain;

    /**
     * 规则配图
     */
    private String ruleImage;

    /**
     * 权益首字母-简拼
     */
    private String shortPY;

    /**
     * 权益弹窗展示状态:1：展示 0不展示'
     */
    private Integer alertState;

    /**
     * 操作人
     */
    private Integer optUserId;

    /**
     * 操作人姓名
     */
    private String optUserName;

    /**
     * 提额信息
     */
    private PlusLiftAmountReq plusLiftAmount;
    /**
     * 发放方式 1_系统发放 2_人工领取
     */
    private Integer sendType;
}
