package com.juzifenqi.plus.dto.resp.order;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 融担咨询费用信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/2 14:01
 */
@Data
public class RdzxCostResp implements Serializable {

    private static final long serialVersionUID = 3129608013938001918L;

    /**
     * 逾期未支付会员费
     */
    private BigDecimal overdueAmount;

    /**
     * 本月需要支付的金额
     */
    private BigDecimal monthAmount;

    /**
     * 本月要支付的订单和借款订单关联+ 逾期订单和借款订单关联
     */
    private List<RdzxOrderCostResp> monthOrderList;
}
