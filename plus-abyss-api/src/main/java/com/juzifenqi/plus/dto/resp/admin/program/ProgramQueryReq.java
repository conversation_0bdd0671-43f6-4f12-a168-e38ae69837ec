package com.juzifenqi.plus.dto.resp.admin.program;

import com.juzifenqi.plus.dto.req.PageEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 方案列表查询入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/12 10:38
 */
@Data
public class ProgramQueryReq extends PageEntity implements Serializable {

    private static final long serialVersionUID = 1779058759829042092L;

    /**
     * 前台方案名称
     */
    private String name;

    /**
     * 方案生效开始时间
     */
    private String startTime;

    /**
     * 方案生效结束时间
     */
    private String endTime;

    /**
     * 会员类型
     */
    private Integer configId;

    /**
     * 生效状态:0 未生效 1 生效
     */
    private Integer programmeStatus;

    /**
     * 上下架状态::1-上架；2-下架
     */
    private Integer status;

    /**
     * 方案id
     */
    private Integer pragramId;

    /**
     * 渠道id
     */
    private Integer channelId;
}
