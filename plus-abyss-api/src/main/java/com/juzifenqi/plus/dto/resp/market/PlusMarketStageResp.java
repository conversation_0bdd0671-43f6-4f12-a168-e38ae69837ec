package com.juzifenqi.plus.dto.resp.market;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 确认借款页-融担卡每期营销
 *
 * <AUTHOR>
 * @date 2024/5/8 上午10:04
 */
@Data
public class PlusMarketStageResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否营销 0 不营销（默认）  1 营销
     */
    private int marketState;

    /**
     * 融担咨询费（会员价格）
     */
    private BigDecimal serviceFee;

    /**
     * 减免金额
     */
    private BigDecimal discountAmount;

    /**
     * 展示位置信息 默认左边 = 1
     */
    private Integer position = 1;

}
