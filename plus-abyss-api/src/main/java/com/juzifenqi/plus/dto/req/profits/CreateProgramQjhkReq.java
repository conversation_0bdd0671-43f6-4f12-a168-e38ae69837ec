package com.juzifenqi.plus.dto.req.profits;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 新增和编辑区间还款优惠折扣配置入参
 * @date 2024/4/2 15:33
 */
@Data
public class CreateProgramQjhkReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 折扣配置
     */
    private BigDecimal discountRate;

    /**
     * 操作人id
     */
    private Integer optId;

    /**
     * 操作人姓名
     */
    private String optName;

}
