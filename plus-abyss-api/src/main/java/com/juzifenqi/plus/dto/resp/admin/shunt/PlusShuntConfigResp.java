package com.juzifenqi.plus.dto.resp.admin.shunt;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 分流配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/20 10:04
 */
@Data
public class PlusShuntConfigResp implements Serializable {

    private static final long serialVersionUID = -1469865837389147436L;

    /**
     * 最小订单金额
     */
    private BigDecimal minOrderAmount;

    /**
     * 最大订单金额
     */
    private BigDecimal maxOrderAmount;

    /**
     * 每日上限订单金额
     */
    private BigDecimal totalOrderAmount;

    /**
     * 每日上限订单笔数
     */
    private Integer totalOrderNum;

    /**
     * 是否重提用户 1_否 2_是
     */
    private Integer resubmitUser;
}
