package com.juzifenqi.plus.dto.resp.detail.land;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 方案营销折扣信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/7 13:46
 */
@Data
public class LandDiscountDetailResp implements Serializable {


    private static final long serialVersionUID = 2640164356422818672L;

    /**
     * 是否符合 默认不符合 = 0  符合：1
     */
    private Integer showDiscountInfo = 0;

    /**
     * 折扣
     */
    private BigDecimal discountRate;

    /**
     * 折扣价格
     */
    private BigDecimal discountPrice;

    /**
     * 结束营销时间和当前时间的相差ms数
     */
    private Long discountEndTime;
}
