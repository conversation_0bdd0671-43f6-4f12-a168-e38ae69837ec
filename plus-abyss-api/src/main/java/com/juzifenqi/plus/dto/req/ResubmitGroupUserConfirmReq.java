package com.juzifenqi.plus.dto.req;

import java.io.Serializable;
import lombok.Data;

/**
 * 重提客群，用户确认结果
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/1 13:51
 */
@Data
public class ResubmitGroupUserConfirmReq implements Serializable {

    private static final long serialVersionUID = 4489164714727812821L;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 确认结果,1=放弃申请，2=确认调整
     */
    private Integer confirmResult;

    /**
     * 点击确认降额按钮类型 1=在页面点击的确认降额按钮  2=在二次弹窗点击的确认降额按钮
     */
    private Integer reduceQuotaButtonType;

    /**
     * 借款单号
     */
    private String orderSn;
}
