package com.juzifenqi.plus.dto.req.admin.common;

import java.io.Serializable;
import lombok.Data;

/**
 * 会员开关配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/5 14:03
 */
@Data
public class SavePlusSwitchControlReq implements Serializable {

    private static final long serialVersionUID = -6500875864386643023L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 开关名称
     */
    private String switchName;

    /**
     * 类型 1_极速退款 2_降息卡自动划扣 3_挽留弹框开关
     */
    private Integer type;

    /**
     * 开关类型 0_极速退卡总开关 5_加速卡开关 8_新人卡开关 9_息费卡极速退款开关 20_降息卡自动划扣开关 30_挽留弹窗开关
     */
    private Integer configId;

    /**
     * 开关状态 1_开启 2_关闭
     */
    private Integer status;

    /**
     * 退款方式 0 立即退款 1延时退款 2立即退款 + 延时退款
     */
    private Integer backMode;

    /**
     * 延时退款间隔时间
     */
    private Integer delayTime;

    /**
     * 开关说明
     */
    private String explain;

    /**
     * 划扣延迟时间（分钟）
     */
    private Integer deductDelay;

    /**
     * 短信延迟时间（分钟）
     */
    private Integer msgDelay;

    /**
     * 划扣类型：1，立即划扣  2，延迟划扣
     */
    private Integer deductType;
}