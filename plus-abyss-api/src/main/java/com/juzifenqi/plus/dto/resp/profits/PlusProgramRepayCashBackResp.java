package com.juzifenqi.plus.dto.resp.profits;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 查询还款返现配置列表返回
 *
 * <AUTHOR>
 * @date 2024/6/11 下午4:36
 */
@Data
public class PlusProgramRepayCashBackResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 借款最小金额
     */
    private BigDecimal minAmount;

    /**
     * 借款最大金额
     */
    private BigDecimal maxAmount;

    /**
     * 借款期数
     */
    private Integer periods;

    /**
     * 返现金额
     */
    private BigDecimal cashbackAmount;

    /**
     * 首次返现比例
     */
    private BigDecimal firstScale;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}
