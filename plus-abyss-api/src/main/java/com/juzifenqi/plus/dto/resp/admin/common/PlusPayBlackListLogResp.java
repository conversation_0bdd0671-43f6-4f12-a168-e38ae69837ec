package com.juzifenqi.plus.dto.resp.admin.common;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 后付款黑名单操作日志
 *
 * <AUTHOR>
 * @date 2022/6/30 10:39
 */
@Data
public class PlusPayBlackListLogResp implements Serializable {

    private static final long serialVersionUID = 8072438087859984123L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 黑名单类型 1-后付款 2-会员黑名单
     */
    private Integer blackType;

    /**
     * 操作人ID
     */
    private Integer optUserId;

    /**
     * 操作姓名
     */
    private String optUserName;

    /**
     * 操作事件:1_批量上传 2_批量删除 3_删除
     */
    private Integer optEvent;

    /**
     * 操作事件名称:1_批量上传 2_批量删除 3_删除
     */
    private String eventName;

    /**
     * 操作内容
     */
    private String content;

    /**
     * 操作时间
     */
    private Date optTime;

    /**
     * bi时间
     */
    private Date biTime;
}
