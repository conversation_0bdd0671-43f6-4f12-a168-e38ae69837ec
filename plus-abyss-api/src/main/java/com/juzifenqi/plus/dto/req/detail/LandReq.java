package com.juzifenqi.plus.dto.req.detail;

import java.io.Serializable;
import lombok.Data;

/**
 * 新落地页入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/3 15:21
 */
@Data
public class LandReq implements Serializable {

    private static final long serialVersionUID = -7394420832889691154L;

    /**
     * 会员类型ID
     */
    private Integer configId;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 用户渠道
     */
    private Integer channelId;

    /**
     * 是否需要分流-落地页入参
     */
    private boolean needShunt;

    /**
     * 场景code
     */
    private Integer sceneCode;

    /**
     * 分流id
     */
    private Integer supplierId;
}
