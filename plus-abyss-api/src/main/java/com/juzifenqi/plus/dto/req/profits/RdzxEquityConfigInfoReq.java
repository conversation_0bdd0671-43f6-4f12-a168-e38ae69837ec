package com.juzifenqi.plus.dto.req.profits;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 融担咨询卡权益配置信息入参
 *
 * <AUTHOR>
 * @date 2024/6/13 下午5:27
 */
@Data
public class RdzxEquityConfigInfoReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 权益0元发放-融担咨询费
     */
    private BigDecimal serviceFee;

    /**
     * 还款返现-借款金额
     */
    private BigDecimal loanAmount;

    /**
     * 期数
     */
    private Integer periods;

}
