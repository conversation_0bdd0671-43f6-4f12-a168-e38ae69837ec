package com.juzifenqi.plus.dto.req.admin.program;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 保存默认方案价入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/12 17:59
 */
@Data
public class SaveDefaultPriceReq implements Serializable {

    private static final long serialVersionUID = 6427612446383452405L;

    /**
     * 定价主表id
     */
    private Integer id;

    /**
     * 渠道id
     */
    private Integer channelId;
    /**
     * 渠道标识
     */
    private Integer bizSource;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 默认方案价列表
     */
    private List<DefaultPrice> priceList;

    /**
     * 操作人
     */
    private String optUser;

    /**
     * 操作人id
     */
    private String optUserId;

    /**
     * 默认方案价
     */
    @Data
    public static class DefaultPrice implements Serializable {

        private static final long serialVersionUID = -1041771450204260740L;

        /**
         * 会员类型id
         */
        private Integer configId;

        /**
         * 默认方案价
         */
        private SavePlusDefaultProgramPriceReq defaultPrice;
    }
}
