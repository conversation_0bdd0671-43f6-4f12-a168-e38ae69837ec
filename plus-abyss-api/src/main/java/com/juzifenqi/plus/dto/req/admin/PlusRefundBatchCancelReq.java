package com.juzifenqi.plus.dto.req.admin;

import com.juzifenqi.plus.dto.req.PageEntity;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 批量取消会员订单入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/28 11:34
 */
@Data
public class PlusRefundBatchCancelReq extends PageEntity implements Serializable {

    private static final long serialVersionUID = 2081935060204665818L;

    /**
     * 批次号
     */
    private String       batchNo;
    /**
     * 批量订单号
     */
    private List<String> orderSns;
    /**
     * 订单号
     */
    private String       orderSn;
    /**
     * 用户id
     */
    private Integer      userId;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 会员类型
     */
    private Integer configId;

    /**
     * 状态: 0_待处理 1_处理中 2_处理成功 3_处理失败
     */
    private Integer optStatus;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 备注
     */
    private String remark;

    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 操作人ID
     */
    private Integer optUserId;

    /**
     * 操作姓名
     */
    private String optUserName;
}
