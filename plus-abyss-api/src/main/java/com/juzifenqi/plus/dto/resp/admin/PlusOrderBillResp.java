package com.juzifenqi.plus.dto.resp.admin;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 订单出入账返回对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/15 14:57
 */
@Data
public class PlusOrderBillResp implements Serializable {

    private static final long serialVersionUID = -8269521838915331005L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 会员方案id
     */
    private Integer programId;

    /**
     * 会员订单号
     */
    private String orderSn;

    /**
     * 支付流水号
     */
    private String serialNumber;

    /**
     * 合作方订单号
     */
    private String supplierOrderSn;

    /**
     * 轨迹状态 1_划扣中 2_划扣成功 3_划扣失败 4_退款成功 5_退款失败
     */
    private Integer trailState;

    /**
     * 签章状态 1_未发起签章 2_已发起签章 3_全部签章成功 4_签章失败
     */
    private Integer signState;

    /**
     * 发起签章重试次数
     */
    private Integer signRetryCount;

    /**
     * 合同上传状态 1_待上传 2_上传中 3_上传成功 4_上传失败
     */
    private Integer contractUploadState;

    /**
     * 发起合同上传重试次数
     */
    private Integer contractUploadRetryCount;

    /**
     * 实际入账方 3_橡树黑卡
     */
    private Integer inSupplier;

    /**
     * 入账金额
     */
    private BigDecimal inAmount;

    /**
     * 入账状态 1_入账成功 2_入账失败 3_重试中
     */
    private Integer inState;

    /**
     * 入账重试次数
     */
    private Integer inRetryCount;

    /**
     * 入账时间_对应入账状态
     */
    private Date inTime;

    /**
     * 入账备注
     */
    private String inRemark;

    /**
     * 实际出账方 0_桔子 3_橡树黑卡
     */
    private Integer outSupplier;

    /**
     * 出账金额
     */
    private BigDecimal outAmount;

    /**
     * 出账状态 1_出账成功 2_出账失败 3_重试中
     */
    private Integer outState;

    /**
     * 出账重试次数
     */
    private int outRetryCount;

    /**
     * 出账时间_对应出账状态
     */
    private Date outTime;

    /**
     * 出账备注
     */
    private String outRemark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
