package com.juzifenqi.plus.dto.req.admin.shunt.separate;

import java.io.Serializable;
import lombok.Data;

/**
 * 新增清分主体入参
 *
 * <AUTHOR>
 */
@Data
public class CreateSeparateSupplierReq implements Serializable {

    private static final long serialVersionUID = -2240616281166913806L;

    /**
     * id（编辑时用到）
     */
    private Integer id;

    /**
     * 主体名称
     */
    private String supplierName;

    /**
     * 商户识别码
     */
    private String merchantId;

    /**
     * 业务场景
     */
    private String businessScene;

    /**
     * 操作人id
     */
    private Integer optUserId;

    /**
     * 操作人名称
     */
    private String optUserName;
}
