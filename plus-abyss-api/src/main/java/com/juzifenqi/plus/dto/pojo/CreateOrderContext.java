package com.juzifenqi.plus.dto.pojo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;
import lombok.Data;

/**
 * 创单上下文
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/30 11:26
 */
@Data
public class CreateOrderContext implements Serializable {

    private static final long serialVersionUID = -5608910600144835339L;

    /**
     * 业务订单号（借款单号-小额月卡/三方单号-哈罗）
     */
    private String loanOrderSn;

    /**
     * 借款金额
     */
    private BigDecimal loanAmount;

    /**
     * 借款期数
     */
    private Integer loanPeriod;

    /**
     * 借款利率
     */
    private BigDecimal loanInterestRate;

    /**
     * 会员订单金额，上下文传入
     */
    private BigDecimal plusAmount;
    /**
     * 联名卡虚拟权益价格，上下文传入
     */
    private BigDecimal lmkAmount;

    /**
     * 订单关联关系业务类型（因为什么业务做关联）
     */
    private Integer relationBusinessType;

    /**
     * 合同编号，如有多个用英文逗号分割
     */
    private String contractNo;

    /**
     * 分流合作方id
     */
    private Integer shuntSupplierId;

    /**
     * 联名卡虚拟权益表主键id
     */
    private Integer virtualId;

    /**
     * 小额月卡首单会员单号，用于落续费单的结清返现权益快照
     */
    private String firstOrderSn;

    /**
     * 支付方式 0_全款 1_划扣  目前是权益卡开通方式=全款支付使用到，与外层payType区分开
     */
    private Integer payType;

    /**
     * 回调地址。目前是权益卡使用到
     */
    private Map<String, String> callbackUrlList;

    /**
     * 收银台支付方式支付完成后跳转到指定页面,目前权益卡用到
     */
    private String paySuccessReturnUrl;

    /**
     * 支付流水号，目前是权益卡用到
     */
    private String serialNumber;

    /**
     * 支付商编，会员不做存储，透传给订单中心
     */
    private String payMerchantCode;

    /**
     * 支付通道，会员不做存储，透传给订单中心
     */
    private String payChannel;

    /**
     * 银卡id
     */
    private Integer bankId;

    /**
     * 资方id
     */
    private Integer capitalId;
}
