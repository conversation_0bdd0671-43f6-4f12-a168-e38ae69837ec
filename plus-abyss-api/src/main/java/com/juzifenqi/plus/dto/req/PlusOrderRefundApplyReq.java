package com.juzifenqi.plus.dto.req;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 会员订单退款申请
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/21 09:50
 */
@Data
public class PlusOrderRefundApplyReq implements Serializable {

    private static final long serialVersionUID = 1056848194904797116L;

    /**
     * 会员订单号
     */
    private String orderSn;

    /**
     * 退卡方式 1_有条件
     */
    private Integer refundType = 1;

    /**
     * 退款比例。可为空
     */
    private BigDecimal discountRate;
}
