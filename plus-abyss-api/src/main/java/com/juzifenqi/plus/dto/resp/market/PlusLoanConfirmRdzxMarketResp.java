package com.juzifenqi.plus.dto.resp.market;

import java.io.Serializable;
import java.util.Map;
import lombok.Data;

/**
 * 确认借款页-融担卡营销返回
 *
 * <AUTHOR>
 * @date 2024/5/8 上午9:58
 */
@Data
public class PlusLoanConfirmRdzxMarketResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否营销 0 不营销（默认）  1 营销
     * <p>融担咨询卡：所有期数都不营销<p/>
     */
    private int allMarketState;

    /**
     * 展示位置信息 默认左边 = 1
     */
    private Integer position = 1;

    /**
     * 每一期的具体营销情况 key 期数 v:
     */
    private Map<String, PlusMarketStageResp> periodMarketDetail;

}
