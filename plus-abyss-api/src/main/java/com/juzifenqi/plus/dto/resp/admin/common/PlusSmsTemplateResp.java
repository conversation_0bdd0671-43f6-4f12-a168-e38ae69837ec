package com.juzifenqi.plus.dto.resp.admin.common;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 短信模板配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/6 15:28
 */
@Data
public class PlusSmsTemplateResp implements Serializable {

    private static final long serialVersionUID = 5196545908664218564L;

    /**
     * id主键自增
     */
    private Integer id;

    /**
     * 模板code码
     */
    private String templateCode;

    /**
     * 短信参数
     */
    private String smsParam;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 操作人
     */
    private String optUser;

    /**
     * 操作人id
     */
    private Integer optUserId;

    /**
     * 参数是否符合配置，默认符合
     */
    private boolean paramOk = true;
}
