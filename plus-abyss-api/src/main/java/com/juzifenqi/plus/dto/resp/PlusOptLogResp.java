package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import java.util.Date;

/**
 * 操作日志
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/21 19:41
 */
public class PlusOptLogResp implements Serializable {

    private static final long serialVersionUID = -6325905807864725158L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 业务id
     */
    private String businessId;

    /**
     * PlusLogBusinessTypeEnum 业务类型
     */
    private Integer businessType;

    /**
     * PlusLogBusinessTypeEnum 业务类型
     */
    private String businessTypeName;


    /**
     * 操作类型id 见枚举PlusOptTypeEnum
     */
    private Integer optType;

    /**
     * 操作类型名称
     */
    private String optName;

    /**
     * 更改之前的内容
     */
    private String befContent;

    /**
     * 更改之后的内容
     */
    private String aftContent;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private Date createTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Integer getOptType() {
        return optType;
    }

    public void setOptType(Integer optType) {
        this.optType = optType;
    }

    public String getOptName() {
        return optName;
    }

    public void setOptName(String optName) {
        this.optName = optName;
    }

    public String getBefContent() {
        return befContent;
    }

    public void setBefContent(String befContent) {
        this.befContent = befContent;
    }

    public String getAftContent() {
        return aftContent;
    }

    public void setAftContent(String aftContent) {
        this.aftContent = aftContent;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getBusinessTypeName() {
        return businessTypeName;
    }

    public void setBusinessTypeName(String businessTypeName) {
        this.businessTypeName = businessTypeName;
    }
}
