package com.juzifenqi.plus.dto.req.admin.program;


import java.io.Serializable;
import lombok.Data;

/**
 * 方案上下架入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/18 19:29
 */
@Data
public class UpProgramReq implements Serializable {

    private static final long serialVersionUID = -144798173684384208L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 状态，1-上架；2-下架
     */
    private Integer status;

    /**
     * 操作人id
     */
    private Integer optUserId;

    /**
     * 操作人姓名
     */
    private String optUserName;
}
