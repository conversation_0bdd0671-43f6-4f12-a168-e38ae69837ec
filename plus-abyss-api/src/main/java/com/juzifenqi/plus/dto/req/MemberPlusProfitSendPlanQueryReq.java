package com.juzifenqi.plus.dto.req;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 发放计划查询请求
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/29 14:41
 */
@Data
public class MemberPlusProfitSendPlanQueryReq implements Serializable {
    private static final long serialVersionUID = -4314223265966102370L;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 会员订单号
     */
    private String orderSn;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 权益类型
     */
    private Integer modelId;

    /**
     * 状态列表（1-未生效 2-达成中 3-已就绪 4-已发放 5-已过期 6-已取消）
     */
    private List<Integer> sendStatuses;
}
