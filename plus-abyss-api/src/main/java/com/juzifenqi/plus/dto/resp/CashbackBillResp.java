package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 返现单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/21 15:37
 */
public class CashbackBillResp implements Serializable {

    private static final long serialVersionUID = 1836151970698588754L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 借款订单号
     */
    private String orderSn;

    /**
     * 会员订单号
     */
    private String plusOrderSn;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 会员类型id
     */
    private Integer modelId;

    /**
     * 返现金额
     */
    private BigDecimal cashbackAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public String getPlusOrderSn() {
        return plusOrderSn;
    }

    public void setPlusOrderSn(String plusOrderSn) {
        this.plusOrderSn = plusOrderSn;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public Integer getModelId() {
        return modelId;
    }

    public void setModelId(Integer modelId) {
        this.modelId = modelId;
    }

    public BigDecimal getCashbackAmount() {
        return cashbackAmount;
    }

    public void setCashbackAmount(BigDecimal cashbackAmount) {
        this.cashbackAmount = cashbackAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
