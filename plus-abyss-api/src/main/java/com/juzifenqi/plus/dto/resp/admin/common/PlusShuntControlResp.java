package com.juzifenqi.plus.dto.resp.admin.common;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 会员分流配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/13 14:30
 */
@Data
public class PlusShuntControlResp implements Serializable {

    private static final long serialVersionUID = -8254985944827188400L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 开关状态 1_关闭 2_开启
     */
    private Integer switchState;

    /**
     * 订单金额最小值
     */
    private BigDecimal minOrderAmount;

    /**
     * 订单金额最大值
     */
    private BigDecimal maxOrderAmount;

    /**
     * 订单总金额
     */
    private BigDecimal orderAmount;

    /**
     * 订单总笔数
     */
    private Integer orderCount;

    /**
     * 桔子分流比例
     */
    private Integer juziRate;

    /**
     * 黑卡分流比例
     */
    private Integer oakRate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 操作人id
     */
    private String optUserId;

    /**
     * 操作人名称
     */
    private String optUserName;
}
