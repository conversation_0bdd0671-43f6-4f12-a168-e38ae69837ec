package com.juzifenqi.plus.dto.resp;

import com.juzifenqi.plus.dto.resp.admin.UserFaceAuthResp;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 取消会员订单返回结构
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/25 17:27
 */
@Data
public class PlusOrderCancelResp implements Serializable {

    private static final long serialVersionUID = 8286619611235136912L;

    /**
     * 每个不能取消的规则，返回的errorCode，用于前端取消会员时候根据code给用户弹出不同的提示信息
     */
    private Integer code;

    /**
     * 原因
     */
    private String reason;

    /**
     * 是否可取消 默认不可取消
     */
    private boolean canBeCancel;

    /**
     * 是否扣减生活权益差价 默认不需扣减
     */
    private boolean needDeductPrice;

    /**
     * 权益数量
     */
    private Integer profitNum;

    /**
     * 差价
     */
    private BigDecimal deductPrice;

    /**
     * 退款金额
     */
    private BigDecimal moneyBack;

    /**
     * 是否超过会员价
     */
    private boolean exceedPlusPrice;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 联名卡会员费
     */
    private BigDecimal lmkPlusAmount;

    /**
     * 联名卡权益金额
     */
    private BigDecimal lmkVirtualAmount;

    /**
     * 联名卡商品名称
     */
    private String virtualProductName;

    /**
     * 是否使用购物返现
     */
    private boolean useGwfx;

    /**
     * 购物返现扣除金额
     */
    private BigDecimal gwfxAmount;

    /**
     * 扣减差价信息列表
     */
    private List<OrderDeductResultResp> deductList;

    /**
     * 用户最新一次人脸识别信息（场景=客服）
     */
    private UserFaceAuthResp userFaceAuth;
}
