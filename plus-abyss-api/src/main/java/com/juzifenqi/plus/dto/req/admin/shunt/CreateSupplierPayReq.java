package com.juzifenqi.plus.dto.req.admin.shunt;

import java.io.Serializable;
import lombok.Data;

/**
 * 新增分流主体支付配置入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/18 10:32
 */
@Data
public class CreateSupplierPayReq implements Serializable {

    private static final long serialVersionUID = 6460488583006906123L;

    /**
     * 划扣通道
     */
    private String deductPayChannel;

    /**
     * 划扣来源
     */
    private String deductPaySource;

    /**
     * 代付商编
     */
    private String defrayMerchantId;

    /**
     * 兜底业务场景
     */
    private String businessScene;

    /**
     * 公司账户号
     */
    private String bankAccountNo;

    /**
     * 公司账户名称
     */
    private String bankAccountName;

    /**
     * 公司账户类型 1_对公 2_对私
     */
    private Integer bankAccountType;

    /**
     * 支行行号
     */
    private String bankBranchNo;

    /**
     * 开户行
     */
    private String bankName;

    /**
     * 开户行所在省名称
     */
    private String bankProvinceName;

    /**
     * 开户行所在省code
     */
    private String bankProvinceCode;

    /**
     * 开户行所在市名称
     */
    private String bankCityName;

    /**
     * 开户行所在市code
     */
    private String bankCityCode;

}
