package com.juzifenqi.plus.dto.req.admin.program;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 差异化定价入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/12 17:59
 */
@Data
public class SaveDiffPriceReq implements Serializable {

    private static final long serialVersionUID = 676885524337252139L;

    /**
     * 定价主表id
     */
    private Integer id;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 渠道标识 1-宜口袋 2-桔多多
     */
    private Integer bizSource;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 提额顺序 1_399-199  2_199-399
     */
    private Integer grade;

    /**
     * 差异化数据列表
     */
    private List<SaveDifferenceProgramPriceReq> diffList;

    /**
     * 操作人
     */
    private String optUser;

    /**
     * 操作人id
     */
    private String optUserId;
}
