package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/9/15 19:37
 **/
@Data
public class PlusAbyssResult<T> implements Serializable {

    public static final Integer GLOBAL_ERROR_CODE = 500;

    public static final Integer GLOBAL_SUCCESS_CODE = 200;

    public static final String GLOBAL_SUCCESS_MSG = "请求成功！";

    public static final String GLOBAL_SYSTEM_ERROR_MSG = "系统可能开小差了...请稍后再尝试！";

    private T       result;
    private Boolean success = true;
    private String  message = "";
    private Integer code;

    private Integer rowsCount;


    public static PlusAbyssResult success() {
        return success(null);
    }

    public static <T> PlusAbyssResult<T> success(Integer rowsCount, T result) {
        return success(rowsCount, GLOBAL_SUCCESS_MSG, result);
    }

    public static <T> PlusAbyssResult<T> success(T result) {
        return success(GLO<PERSON>L_SUCCESS_MSG, result);
    }

    public static <T> PlusAbyssResult<T> success(String message, T result) {
        return success(null, message, result);
    }

    public static <T> PlusAbyssResult<T> success(Integer rowsCount, String message, T result) {
        PlusAbyssResult data = new PlusAbyssResult();
        data.setSuccess(true);
        data.setRowsCount(rowsCount);
        data.setCode(GLOBAL_SUCCESS_CODE);
        data.setMessage(message);
        data.setResult(result);
        return data;
    }

    public static <T> PlusAbyssResult<T> error(Integer code, String message) {
        PlusAbyssResult data = new PlusAbyssResult();
        data.setSuccess(false);
        data.setCode(code);
        data.setMessage(message);
        return data;
    }

    public static <T> PlusAbyssResult<T> error(String message) {
        return error(GLOBAL_ERROR_CODE, message);
    }

    public static <T> PlusAbyssResult<T> error() {
        return error(GLOBAL_SYSTEM_ERROR_MSG);
    }
}
