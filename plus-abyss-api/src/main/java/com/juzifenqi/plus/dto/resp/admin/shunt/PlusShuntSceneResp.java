package com.juzifenqi.plus.dto.resp.admin.shunt;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/26  9:46
 * @description
 */
@Data
public class PlusShuntSceneResp implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 配置id
     */
    private Integer configId;

    /**
     * 业务场景
     */
    private String businessScene;

    /**
     * 最大订单金额
     */
    private BigDecimal maxOrderAmount;

    /**
     * 优先级
     */
    private Integer priorityOrder;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
