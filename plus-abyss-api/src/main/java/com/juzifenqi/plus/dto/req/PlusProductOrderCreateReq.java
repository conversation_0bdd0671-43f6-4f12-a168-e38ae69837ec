package com.juzifenqi.plus.dto.req;

import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2024/2/5 15:35
 * @description
 */
@Data
public class PlusProductOrderCreateReq implements Serializable {

    private static final long serialVersionUID = -5719732225911552801L;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 商品id
     */
    private Integer productId;

    /**
     * 商品sku
     */
    private String productSku;

    /**
     * 会员单号
     */
    private String orderSn;

    /**
     * 收货地址id
     */
    private Integer addressId;

    /**
     * 权益id
     */
    private Integer modelId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 货品id
     */
    private Integer productGoodsId;

    /**
     * token
     */
    private String token;

    /**
     * imei
     */
    private String imei;

    /**
     * 来源
     */
    private Integer source;

    /**
     * 版本号
     */
    private String versionCode;

    /**
     * ip
     */
    private String ip;
}
