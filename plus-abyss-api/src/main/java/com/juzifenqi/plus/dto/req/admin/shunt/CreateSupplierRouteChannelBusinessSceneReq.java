package com.juzifenqi.plus.dto.req.admin.shunt;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

 /**
 * <AUTHOR>
 * @date 2025/6/26  9:46
 * @description 创建分流路由配置渠道业务场景入参
 */
@Data
public class CreateSupplierRouteChannelBusinessSceneReq implements Serializable {

    private static final long serialVersionUID = 4430595416160306413L;

    /**
     * 收款金额上限
     */
    private BigDecimal maxOrderAmount;

    /**
     * 支付业务场景
     */
    private String businessScene;

    /**
     * 收款方式优先级
     */
    private Integer priorityOrder;
}
