package com.juzifenqi.plus.dto.req.admin.program;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 创建渠道管理入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/5 19:50
 */
@Data
public class CreateChannelManagerReq implements Serializable {

    private static final long serialVersionUID = -9100468764141676486L;

    /**
     * 渠道ID
     */
    private Integer channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 复用渠道ID，没有则为null
     */
    private Integer reuseChannelId;

    /**
     * 操作人id
     */
    private Integer optUserId;

    /**
     * 操作人名称
     */
    private String optUserName;
}
