package com.juzifenqi.plus.dto.resp.admin;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 续费信息列表返参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/15 16:46
 */
@Data
public class PlusRenewResp implements Serializable {

    private static final long serialVersionUID = 42L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 渠道
     */
    private Integer channel;

    /**
     * 会员方案ID
     */
    private Integer programId;

    /**
     * 卡ID
     */
    private Integer bankId;

    /**
     * 续费价格
     */
    private BigDecimal money;

    /**
     * 订单ID
     */
    private String orderSn;

    /**
     * 0划扣失效 ，1有效, 2方案已下架导致失效
     */
    private Integer state;

    /**
     * 续费次数
     */
    private Integer renewNum;

    /**
     * 取消续费时间
     */
    private Date cancelRenewTime;
    /**
     * 会员类型
     */
    private Integer configId;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 操作人ID，系统操作
     */
    private Integer operatingId;

    /**
     * 操作人
     */
    private String operatingName;
}