package com.juzifenqi.plus.dto.req;

import com.juzifenqi.plus.enums.PlusPayTypeEnum;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 主动划扣入参
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/14 16:10
 */
public class PlusDeductForActiveReq implements Serializable {

    private static final long serialVersionUID = 3743811274835820310L;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 划扣类型：PlusPayTypeEnum
     */
    private PlusPayTypeEnum deductFlag;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 会员类型
     */
    private Integer configId;

    /**
     * 订单渠道id
     */
    private Integer orderChannelId;

    /**
     * 订单创单时间
     */
    private Date createOrderTime;

    /**
     * 会员订单号
     */
    private String plusOrderSn;

    /**
     * 放款银行卡id（划扣卡）
     */
    private Integer bankId;

    /**
     * 划扣次数
     */
    private Integer deductNum;

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public PlusPayTypeEnum getDeductFlag() {
        return deductFlag;
    }

    public void setDeductFlag(PlusPayTypeEnum deductFlag) {
        this.deductFlag = deductFlag;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public Integer getConfigId() {
        return configId;
    }

    public void setConfigId(Integer configId) {
        this.configId = configId;
    }

    public Integer getOrderChannelId() {
        return orderChannelId;
    }

    public void setOrderChannelId(Integer orderChannelId) {
        this.orderChannelId = orderChannelId;
    }

    public Date getCreateOrderTime() {
        return createOrderTime;
    }

    public void setCreateOrderTime(Date createOrderTime) {
        this.createOrderTime = createOrderTime;
    }

    public String getPlusOrderSn() {
        return plusOrderSn;
    }

    public void setPlusOrderSn(String plusOrderSn) {
        this.plusOrderSn = plusOrderSn;
    }

    public Integer getBankId() {
        return bankId;
    }

    public void setBankId(Integer bankId) {
        this.bankId = bankId;
    }

    public Integer getDeductNum() {
        return deductNum;
    }

    public void setDeductNum(Integer deductNum) {
        this.deductNum = deductNum;
    }
}
