package com.juzifenqi.plus.dto.resp.admin.shunt;

import com.juzifenqi.plus.enums.supplier.SupplierSeparateEnableStateEnum;
import com.juzifenqi.plus.enums.supplier.SupplierSettleEnableStateEnum;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分流主体
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/18 09:51
 */
@Data
public class PlusShuntSupplierResp implements Serializable {

    private static final long serialVersionUID = -483949265020640052L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 主体名称
     */
    private String supplierName;

    /**
     * 业务场景列表
     */
    private List<PlusShuntSupplierBusinessSceneResp> businessSceneList;

    /**
     * 是否需要清分
     *
     * @see SupplierSeparateEnableStateEnum
     */
    private Integer separateEnableState;

    /**
     * 是否需要结算
     *
     * @see SupplierSettleEnableStateEnum
     */
    private Integer settleEnableState;

    /**
     * 结算模式 1-普通 2-循环
     *
     * @see com.juzifenqi.plus.enums.supplier.SettleModeEnum
     */
    private Integer settleMode;

    /**
     * 是否启用 1_否 2_是
     */
    private Integer enableState;

    /**
     * 新清分信息
     */
    private List<PlusShuntSupplierSeparateResp> separateChannelList;

    /**
     * 支付参数
     */
    private PlusShuntSupplierPayResp pay;

    /**
     * 合同列表
     */
    private List<PlusShuntSupplierContractResp> contractList;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
