package com.juzifenqi.plus.api;

import com.juzifenqi.plus.dto.resp.PlusAbyssResult;

/**
 * Description: 会员主体相关Api
 *
 * <AUTHOR>
 * @date created on 2025/6/19
 */
public interface IPlusSupplierJobApi {

    /**
     * 会员主体退费账户余额不足报警Job
     */
    PlusAbyssResult<Boolean> plusSupplierLimitAlarmJob();


    /**
     * 会员订单退款监控Job
     *
     * @return
     */
    PlusAbyssResult<Boolean> refundPlusOrderMonitor(Integer lastId);

    /**
     * 刷新会员配置缓存job
     *
     * @param supplierId
     * @return
     */
    void refreshSupplierCfgCache(Integer supplierId);


}
