package com.juzifenqi.plus.enums;

import java.util.Objects;

/**
 * 虚拟商品不能购买原因枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/19 2:37 PM
 */
public enum VirtualCanBuyProductEnum {

    /**
     * <p>-1、异常情况<p/>
     * <p>0、已领取该权益 （会员权益周期内已买过一次）<p/>
     * <p>1、下月再来 （30天一周期，当月已买过）<p/>
     * <p>2、有三方处理中订单 <p/>
     * <p>3、当月领取权益种类已达上限 <p/>
     * <p>4、有效期内领取权益种类已达上限 <p/>
     * <p>5、全部已领取 <p/>
     */
    CAN_BUY_EXCEPTION(-1, "异常情况", "购买异常,请联系客服处理"), CAN_BUY_ZERO(0,
            "会员权益周期内已买过一次", "您已领取该权益，祝您使用愉快"), CAN_BUY_ONE(1, "当月已买过",
            "该权益本月已领取，请下月再来呦"), CAN_BUY_TWO(2, "有三方处理中订单",
            "您当前有处理中订单，请稍后再试"), CAN_BUY_THREE(3, "当月领取权益种类已达上限",
            "当月领取权益种类已达上限"), CAN_BUY_FOUR(4, "有效期内领取权益种类已达上限",
            "有效期内领取权益种类已达上限"), CAN_BUY_FIVE(5, "全部已领取",
            "您有效期内可领取次数已达上限"), CAN_BUY_SIX(6, "当前周期领取权益种类已达上限",
            "当前周期您领取权益已达上限"), CAN_BUY_SEVEN(7, "当前周期已买过",
            "该权益当前周期已领取");

    private Integer code;

    private String remark;

    /**
     * 对客提示
     */
    private String tipMsg;


    private VirtualCanBuyProductEnum(Integer code, String remark, String tipMsg) {
        this.code = code;
        this.remark = remark;
        this.tipMsg = tipMsg;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTipMsg() {
        return tipMsg;
    }

    public static String getTipMsgByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (VirtualCanBuyProductEnum value : VirtualCanBuyProductEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getTipMsg();
            }
        }
        return null;
    }
}
