package com.juzifenqi.plus.config;

import com.alibaba.dubbo.config.ReferenceConfig;
import com.juzifenqi.plus.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/9/14 10:17
 **/
@Component
public class DubboConsumer {

    @Autowired
    private DubboConfigBuilder dubboConfigBuilder;
    @Autowired
    private DubboProperties    dubboProperties;

    @Bean(name = "plusOrderJobApi")
    public IPlusOrderJobApi getIPlusOrderJobApi() {
        ReferenceConfig<IPlusOrderJobApi> build = dubboConfigBuilder.build(IPlusOrderJobApi.class,
                dubboProperties.abyssGroup);
        return build.get();
    }

    @Bean(name = "settleJobApi")
    public ISettleJobApi getISettleJobApi() {
        ReferenceConfig<ISettleJobApi> build = dubboConfigBuilder.build(ISettleJobApi.class,
                dubboProperties.abyssGroup);
        return build.get();
    }

    @Bean(name = "plusMarketLogApi")
    public IPlusMarketingLogApi getIPlusMarketingLogApi() {
        ReferenceConfig<IPlusMarketingLogApi> build = dubboConfigBuilder.build(IPlusMarketingLogApi.class,
                dubboProperties.abyssGroup);
        return build.get();
    }

    @Bean(name = "plusSupplierJobApi")
    public IPlusSupplierJobApi getIPlusSupplierJobApi() {
        ReferenceConfig<IPlusSupplierJobApi> build = dubboConfigBuilder.build(IPlusSupplierJobApi.class,
                dubboProperties.abyssGroup);
        return build.get();
    }

    @Bean(name = "plusShuntJobApi")
    public IPlusShuntJobApi getIPlusShuntJobApi() {
        ReferenceConfig<IPlusShuntJobApi> build = dubboConfigBuilder.build(IPlusShuntJobApi.class,
                dubboProperties.abyssGroup);
        return build.get();
    }

    @Bean(name = "plusMonthMemberApi")
    public IPlusMonthMemberApi getIPlusMonthMemberApi() {
        ReferenceConfig<IPlusMonthMemberApi> build = dubboConfigBuilder.build(IPlusMonthMemberApi.class,
                dubboProperties.abyssGroup);
        return build.get();
    }

    @Bean(name = "plusOrderApi")
    public IPlusOrderApi getIPlusOrderApi() {
        ReferenceConfig<IPlusOrderApi> build = dubboConfigBuilder.build(IPlusOrderApi.class,
                dubboProperties.abyssGroup);
        return build.get();
    }
}
