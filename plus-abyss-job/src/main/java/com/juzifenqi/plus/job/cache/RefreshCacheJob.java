package com.juzifenqi.plus.job.cache;

import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusSupplierJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 刷新缓存任务
 *
 * <AUTHOR>
 * @date 2025/8/10
 */
@Slf4j
@Component
public class RefreshCacheJob {

    @Resource
    private IPlusSupplierJobApi plusSupplierJobApi;

    @XxlJob("refreshSupplierConfigJob")
    public ReturnT<String> refreshSupplierConfig(String param) {
        log.info("刷新会员配置缓存开始:{}", param);

        try {
            Integer supplierId = null;
            if (StringUtils.isNotBlank(param)) {
                supplierId = Integer.valueOf(param);
            }
            plusSupplierJobApi.refreshSupplierCfgCache(supplierId);

            log.info("刷新会员配置缓存结束:{}", param);
        } catch (Exception e) {
            LogUtil.printLog("刷新会员配置缓存异常", e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

}
