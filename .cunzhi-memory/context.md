# 项目上下文信息

- 新增会员月卡续费计划功能模块，表名：plus_month_member_renewal_plan，包含批量插入、订单号查询、分页查询功能
- PlusOrderApplicationImpl.createPlusOrder方法是会员订单创建的核心入口，包含渠道校验、订单创建、会员开通等完整业务流程，使用事务保证数据一致性，采用策略模式处理不同会员类型
- supplierId字段来源：使用 PlusOrderShuntPo.planInSupplier 字段作为续费计划的收款主体ID
- 已完成会员月卡定时扣费功能实现：1.新增IPlusOrderRepository.getMonthCardOrdersForDeduction查询方法 2.修改PlusOrderApplicationImpl.deduct方法跳过会员月卡续费订单前置校验 3.新增PlusMonthMemberJob.plusMonthMemberDeduct定时任务方法
- 已完成会员月卡定时扣费功能实现：1.在IPlusOrderRepository接口中新增getMonthCardOrdersForDeduction分页查询方法 2.在OrderRepositoryImpl中实现getMonthCardOrdersForDeduction方法 3.在IPlusOrderInfoMapper中新增getMonthCardOrdersForDeduction查询方法 4.修改PlusOrderApplicationImpl.deduct方法，为会员月卡续费订单(configId=16且期数>1)跳过前置校验 5.在PlusOrderApplicationImpl中新增monthCardDeductJob、processMonthCardDeduction、shouldDeductNow、buildDeductEvent等方法实现定时扣费逻辑 6.在IPlusOrderApi接口中新增monthCardDeductJob方法 7.在PlusMonthMemberJob中新增plusMonthMemberDeduct定时任务方法
- 已完成IPlusOrderInfoMapper.getMonthCardOrdersForDeduction方法的MyBatis XML SQL实现：在PlusOrderInfoMapper.xml中添加了查询方法，查询条件为order_state=1(待支付)、config_id=16(会员月卡)、month_period>1(期数大于1)，按create_time升序排序，支持分页查询，使用了索引提示idx_order_state_config_month_create优化性能
