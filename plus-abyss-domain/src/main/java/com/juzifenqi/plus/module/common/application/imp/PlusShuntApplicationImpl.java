package com.juzifenqi.plus.module.common.application.imp;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.dto.req.PageEntity;
import com.juzifenqi.plus.enums.EnableStateEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.common.IFmsRepository;
import com.juzifenqi.plus.module.common.IPlusShuntRepository;
import com.juzifenqi.plus.module.common.application.IPlusShuntApplication;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.common.entity.shunt.DefrayMerchantBalanceRespEntity;
import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntPayAlarmEntity;
import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntRouteConfigEntity;
import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierEntity;
import com.juzifenqi.plus.module.common.event.shunt.CreateSupplierEvent;
import com.juzifenqi.plus.module.common.event.shunt.CreateSupplierRouteEvent;
import com.juzifenqi.plus.module.common.event.shunt.EditSupplierPublicConfigEvent;
import com.juzifenqi.plus.module.common.event.shunt.EnableSupplierEvent;
import com.juzifenqi.plus.module.common.event.shunt.separate.CreateSeparateSupplierEvent;
import com.juzifenqi.plus.module.common.event.shunt.separate.EnableSeparateSupplierEvent;
import com.juzifenqi.plus.module.common.repository.external.acl.feishu.FeiShuModel;
import com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntSupplierPo;
import com.juzifenqi.plus.utils.RedisLock;

import java.math.BigDecimal;
import java.util.*;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 分流配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/18 10:57
 */
@Slf4j
@Service
public class PlusShuntApplicationImpl implements IPlusShuntApplication {

    @Autowired
    private IPlusShuntRepository shuntRepository;
    @Autowired
    private RedisLock            redisLock;
    @Resource
    private IFmsRepository fmsRepository;
    @Resource
    private FeiShuModel feiShuModel;
    @Resource
    private ConfigProperties configProperties;

    @Override
    public void addSupplier(CreateSupplierEvent event) {
        String redisKey = RedisConstantPrefix.ADD_SUPPLIER_RESUBMIT + event.getSupplierName();
        boolean lock = redisLock.lock(redisKey, "1", 3);
        if (!lock) {
            throw new PlusAbyssException("请勿重复添加主体");
        }
        shuntRepository.addSupplier(event);
    }

    @Override
    public void editSupplier(CreateSupplierEvent event) {
        String redisKey = RedisConstantPrefix.EDIT_SUPPLIER_RESUBMIT + event.getSupplierName();
        boolean lock = redisLock.lock(redisKey, "1", 3);
        if (!lock) {
            throw new PlusAbyssException("请勿重复编辑主体");
        }
        shuntRepository.editSupplier(event);
    }

    @Override
    public PageResultEntity<PlusShuntSupplierEntity> getSupplierPageList(Integer supplierType,
            PageEntity page) {
        return shuntRepository.getSupplierPageList(supplierType, page);
    }

    @Override
    public List<PlusShuntSupplierEntity> getSupplierList(Integer supplierType,
            Integer enableState) {
        return shuntRepository.getSupplierList(supplierType, enableState);
    }

    @Override
    public PlusShuntSupplierEntity getSupplierDetail(Integer id) {
        return shuntRepository.getSupplierDetail(id);
    }

    @Override
    public void enableSupplier(EnableSupplierEvent event) {
        String redisKey =
                Objects.equals(event.getEnableState(), EnableStateEnum.DISABLE.getCode()) ?
                        RedisConstantPrefix.DISABLE_SUPPLIER_RESUBMIT + event.getId()
                        : RedisConstantPrefix.ENABLE_SUPPLIER_RESUBMIT + event.getId();
        boolean lock = redisLock.lock(redisKey, "1", 3);
        if (!lock) {
            throw new PlusAbyssException("请勿重复启用/停用主体配置");
        }
        shuntRepository.enableSupplier(event);
    }

    @Override
    public void editPublicConfig(EditSupplierPublicConfigEvent event) {
        boolean lock = redisLock.lock(RedisConstantPrefix.EDIT_SUPPLIER_PUBLIC, "1", 3);
        if (!lock) {
            throw new PlusAbyssException("请勿重复编辑");
        }
        shuntRepository.editPublicConfig(event);
    }

    @Override
    public void addRoute(CreateSupplierRouteEvent event) {
        boolean lock = redisLock.lock(RedisConstantPrefix.ADD_SHUNT_ROUTE + event.getSupplierId(),
                "1", 3);
        if (!lock) {
            throw new PlusAbyssException("请勿重复添加路由配置：" + event.getSupplierName());
        }
        shuntRepository.addRoute(event);
    }

    @Override
    public void editRoute(CreateSupplierRouteEvent event) {
        boolean lock = redisLock.lock(RedisConstantPrefix.EDIT_SHUNT_ROUTE + event.getId(), "1", 3);
        if (!lock) {
            throw new PlusAbyssException("请勿重复编辑路由配置：" + event.getSupplierName());
        }
        shuntRepository.editRoute(event);
    }

    @Override
    public void enableRoute(EnableSupplierEvent event) {
        String key = event.getEnableState().equals(EnableStateEnum.ENABLE.getCode()) ?
                RedisConstantPrefix.ENABLE_SHUNT_ROUTE + event.getId()
                : RedisConstantPrefix.DISABLE_SHUNT_ROUTE + event.getId();
        boolean lock = redisLock.lock(key, "1", 3);
        if (!lock) {
            throw new PlusAbyssException("请勿重复操作");
        }
        shuntRepository.enableRoute(event);
    }

    @Override
    public PlusShuntRouteConfigEntity getShuntDetail() {
        return shuntRepository.getShuntDetail();
    }

    @Override
    public void addSeparateSupplier(CreateSeparateSupplierEvent event) {
        String redisKey =
                RedisConstantPrefix.ADD_SEPARATE_SUPPLIER_RESUBMIT + event.getSupplierName();
        boolean lock = redisLock.lock(redisKey, "1", 3);
        if (!lock) {
            throw new PlusAbyssException("请勿重复添加清分主体");
        }
        shuntRepository.addSeparateSupplier(event);
    }

    @Override
    public void editSeparateSupplier(CreateSeparateSupplierEvent event) {
        String redisKey =
                RedisConstantPrefix.EDIT_SEPARATE_SUPPLIER_RESUBMIT + event.getSupplierName();
        boolean lock = redisLock.lock(redisKey, "1", 3);
        if (!lock) {
            throw new PlusAbyssException("请勿重复编辑清分主体");
        }
        shuntRepository.editSeparateSupplier(event);
    }

    @Override
    public void enableSeparateSupplier(EnableSeparateSupplierEvent event) {
        String redisKey =
                Objects.equals(event.getEnableState(), EnableStateEnum.DISABLE.getCode()) ?
                        RedisConstantPrefix.DISABLE_SEPARATE_SUPPLIER_RESUBMIT + event.getId()
                        : RedisConstantPrefix.ENABLE_SEPARATE_SUPPLIER_RESUBMIT + event.getId();
        boolean lock = redisLock.lock(redisKey, "1", 3);
        if (!lock) {
            throw new PlusAbyssException("请勿重复启用/停用清分主体配置");
        }
        shuntRepository.enableSeparateSupplier(event);
    }

    @Override
    public PlusShuntSupplierEntity loadPlusShuntSupplier(Integer id) {
        return shuntRepository.loadPlusShuntSupplier(id);
    }

    @Override
    public Boolean plusSupplierLimitAlarm() {

        /*
         * 查询需要check报警的会员主体商户, 因桔子侧商户名称、商户编号为1对多关系, 因此使用Nacos配置方案
         * 查询失败: job重试
         */
        /*List<PlusShuntSupplierEntity> checkList;
        try {
            checkList = shuntRepository.getLimitCheckSupplierList();
            log.info("查询待处理会员主体退费账户列表: {}", JSON.toJSONString(checkList));
        } catch (Exception e) {
            log.error("查询待处理会员主体退费账户异常", e);
            feiShuModel.sendTextMsg("查询待处理会员主体退费账户 - 系统异常, msg = " + e.getMessage());
            return false;
        }*/

        /*
         * 配置中心获取待处理列表
         * 查询失败: job重试
         */
        if (StringUtils.isBlank(configProperties.payAlarmPendingList)) {
            return true;
        }

        List<PlusShuntPayAlarmEntity> checkList = JSON.parseArray(configProperties.payAlarmPendingList, PlusShuntPayAlarmEntity.class);
        if (CollectionUtils.isEmpty(checkList)) {
            return true;
        }

        /*
         * 查询商户账户余额不支持批量查询, 单条处理, 目前待处理商户数量较少, 因此不需要多线程处理
         * 单条处理失败: 发送飞书报警, 人工处理
         */
        checkList.forEach(item -> {
            try {
                log.info("单会员主体退费账户余额不足报警 - 业务处理开始: {}", JSON.toJSONString(item));
                if (item.getLimitAlarmThreshold() == null || StringUtils.isBlank(item.getDefrayMerchantId())) {
                    return;
                }
                BigDecimal threshold = item.getLimitAlarmThreshold();
                DefrayMerchantBalanceRespEntity channelMerBalance = fmsRepository.getChannelMerBalance(item.getDefrayMerchantId());
                if (channelMerBalance == null || channelMerBalance.getAvailableAmount() == null) {
                    feiShuModel.sendTextMsg("账户余额查询为空, 请及时处理");
                    return;
                }
                // 可用余额小于阈值触发业务报警
                if (threshold.compareTo(channelMerBalance.getAvailableAmount()) > 0) {
                    log.info("单会员主体退费账户余额不足报警 - 余额不足 threshold: {}, availableAmount: {}", threshold, channelMerBalance.getAvailableAmount());
                    String text = "宜口袋会员退款账户余额不足预警, 退款账户: " +
                            item.getSupplierName() +
                            ", 商户号: " +
                            item.getDefrayMerchantId() +
                            ", 阈值: " +
                            threshold +
                            "元, 当前余额: " +
                            channelMerBalance.getAvailableAmount() +
                            "元, 请及时充值!";
                    feiShuModel.sendBizAlertMsg(text, configProperties.feishuMerLimitUrl, new HashMap<String, String>(){{put("all", "所有人");}});
                }
                log.info("单会员主体退费账户余额不足报警 - 业务处理结束 item: {}, channelMerBalance: {}", JSON.toJSONString(item), JSON.toJSONString(channelMerBalance));
            } catch (PlusAbyssException e) {
                feiShuModel.sendTextMsg("单会员主体退费账户余额不足报警-业务处理异常, msg = " + e.getMessage());
            } catch (Exception e) {
                log.error("单会员主体退费账户余额不足报警 - 系统异常", e);
                feiShuModel.sendTextMsg("单会员主体退费账户余额不足报警 - 系统异常, msg = " + e.getMessage());
            }
        });

        return true;
    }

    @Override
    public void initSupplierBankAccountNoUuid(Integer size) {
        shuntRepository.initSupplierBankAccountNoUuid( size);
    }

    @Override
    public void inspectSupplierBankAccountNoUuid(Integer size) {
        shuntRepository.inspectSupplierBankAccountNoUuid(size);
    }

    @Override
    public void refreshSupplierCfgCache(Integer supplierId) {
        shuntRepository.refreshSupplierCfgCache(supplierId);
    }
}
