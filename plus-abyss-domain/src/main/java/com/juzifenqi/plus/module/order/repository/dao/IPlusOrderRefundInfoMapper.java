package com.juzifenqi.plus.module.order.repository.dao;

import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderRefundInfoPo;

import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 会员订单退款信息
 *
 * <AUTHOR>
 * @date 2024/9/2 10:19
 */
@Mapper
public interface IPlusOrderRefundInfoMapper {

    /**
     * 新增返回ID
     */
    Long save(PlusOrderRefundInfoPo plusOrderRefundInfo);

    /**
     * 新增
     */
    Integer insertPlusOrderRefundInfo(
            @Param("plusOrderRefundInfo") PlusOrderRefundInfoPo plusOrderRefundInfo);

    /**
     * 修改退款状态
     */
    Integer updateRefundState(@Param("refundSerialNo") String refundSerialNo,
            @Param("refundState") Integer refundState);

    /**
     * 根据id修改
     */
    Integer updateById(PlusOrderRefundInfoPo refundInfoPo);

    /**
     * 根据订单号、状态查询
     */
    List<PlusOrderRefundInfoPo> getByOrderSn(@Param("orderSn") String orderSn,
            List<Integer> refundStateList);

    /**
     * 根据退款业务流水号查询
     */
    PlusOrderRefundInfoPo getByRefundSerialNo(@Param("refundSerialNo") String refundSerialNo);


    PlusOrderRefundInfoPo getById(@Param("id") Long id);

    /**
     * 修改支付退款流水号
     */
    Integer updatePaySerialNo(@Param("refundSerialNo") String refundSerialNo,
            @Param("paySerialNo") String paySerialNo);

    /**
     * 修改支付退款流水号
     */
    Integer updatePaySerialNoById(@Param("id") Long id, @Param("paySerialNo") String paySerialNo);

    /**
     * 根据订单号查询最近一条退款信息
     *
     * @param orderSn 会员订单号
     */
    PlusOrderRefundInfoEntity getLastOneByOrderSn(String orderSn);

    /**
     * 根据退款状态查询
     * @param status 退款状态
     * @param time 时间
     * @param limit  数量
     * @return list
     */
    List<PlusOrderRefundInfoEntity> getRefundInfoByStatus(@Param("status") Integer status, @Param("time") LocalDateTime time, @Param("endTime") LocalDateTime endTime
            ,@Param("lastId") Integer lastId,@Param("limit") Integer limit);
}
