package com.juzifenqi.plus.module.order.repository.impl;

import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRefundInfoRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundDetailEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity;
import com.juzifenqi.plus.module.order.repository.converter.IPlusOrderRefundInfoRepositoryConverter;
import com.juzifenqi.plus.module.order.repository.dao.IPlusOrderRefundDetailMapper;
import com.juzifenqi.plus.module.order.repository.dao.IPlusOrderRefundInfoMapper;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderRefundDetailPo;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderRefundInfoPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员订单退款信息
 *
 * <AUTHOR>
 * @date 2024/9/2 11:02
 */
@Repository
public class PlusOrderRefundInfoRepositoryImpl implements IPlusOrderRefundInfoRepository {

    private final IPlusOrderRefundInfoRepositoryConverter convert = IPlusOrderRefundInfoRepositoryConverter.instance;

    @Autowired
    private IPlusOrderRefundInfoMapper plusOrderRefundInfoMapper;
    @Autowired
    private IPlusOrderRefundDetailMapper plusOrderRefundDetailMapper;

    @Override
    public List<PlusOrderRefundInfoEntity> getByOrderSn(String orderSn,
            List<Integer> refundStateList) {
        return convert.toPlusOrderRefundInfoEntityList(
                plusOrderRefundInfoMapper.getByOrderSn(orderSn, refundStateList));
    }

    @Override
    public Long save(PlusOrderRefundInfoEntity entity) {
        PlusOrderRefundInfoPo plusOrderRefundInfoPo = convert.toPlusOrderRefundInfoPo(entity);
        plusOrderRefundInfoMapper.save(plusOrderRefundInfoPo);
        return plusOrderRefundInfoPo.getId();
    }

    @Override
    public Long saveInfoAndDetail(PlusOrderRefundInfoEntity entity, List<PlusOrderRefundDetailEntity> details) {
        //保存主表
        PlusOrderRefundInfoPo plusOrderRefundInfoPo = convert.toPlusOrderRefundInfoPo(entity);
        plusOrderRefundInfoMapper.save(plusOrderRefundInfoPo);
        //保存明细
        details.forEach(t -> {
            t.setRefundInfoId(plusOrderRefundInfoPo.getId());
            t.setTotalPeriod(details.size());
        });
        plusOrderRefundDetailMapper.batchInsert(convert.toPlusOrderRefundDetailPoList(details));
        return plusOrderRefundInfoPo.getId();
    }

    @Override
    public void saveDetail(PlusOrderRefundDetailEntity entity) {
        PlusOrderRefundDetailPo plusOrderRefundDetailPo = convert.toPlusOrderRefundDetailPo(entity);
        plusOrderRefundDetailMapper.save(plusOrderRefundDetailPo);
        entity.setId(plusOrderRefundDetailPo.getId());
    }

    @Override
    public PlusOrderRefundInfoEntity getById(Long id) {
        return convert.toPlusOrderRefundInfoEntity(plusOrderRefundInfoMapper.getById(id));
    }

    @Override
    public PlusOrderRefundInfoEntity getByRefundSerialNo(String refundSerialNo) {
        return convert.toPlusOrderRefundInfoEntity(
                plusOrderRefundInfoMapper.getByRefundSerialNo(refundSerialNo));
    }

    @Override
    public PlusOrderRefundDetailEntity getDetailByRefundSerialNo(String refundSerialNo) {
        return convert.toPlusOrderRefundDetailEntity(plusOrderRefundDetailMapper.getDetailByRefundSerialNo(refundSerialNo));
    }

    @Override
    public PlusOrderRefundDetailEntity getDetailById(Long refundDetailId) {
        return convert.toPlusOrderRefundDetailEntity(plusOrderRefundDetailMapper.getDetailById(refundDetailId));
    }

    @Override
    public List<PlusOrderRefundDetailEntity> getDetailsByInfoId(Long refundInfoId) {
        return convert.toPlusOrderRefundDetailEntityList(plusOrderRefundDetailMapper.getDetailsByInfoId(refundInfoId));
    }

    @Override
    public void updateRefundState(String refundSerialNo, Integer refundState) {
        plusOrderRefundInfoMapper.updateRefundState(refundSerialNo, refundState);
    }

    @Override
    public void updateRefundInfoAndDetailState(Long refundInfoId, List<Long> refundDetailIds, Integer refundState) {
        plusOrderRefundDetailMapper.updateStateByIds(refundDetailIds, refundState);
        PlusOrderRefundInfoPo forUpdate = new PlusOrderRefundInfoPo();
        forUpdate.setId(refundInfoId);
        forUpdate.setRefundState(refundState);
        plusOrderRefundInfoMapper.updateById(forUpdate);
    }


    @Override
    public void updateRefundDetailBySerialNo(String serialNo, String paySerialNo) {
        plusOrderRefundDetailMapper.updateDetailBySerialNo(serialNo, paySerialNo);
    }

    @Override
    public void updateRefundDetail(PlusOrderRefundDetailEntity detailEntity) {
        plusOrderRefundDetailMapper.updateDetail(convert.toPlusOrderRefundDetailPo(detailEntity));
    }

    @Override
    public void updatePaySerialNo(String refundSerialNo, String paySerialNo) {
        plusOrderRefundInfoMapper.updatePaySerialNo(refundSerialNo, paySerialNo);
    }

    @Override
    public void updatePaySerialNo(Long id, String paySerialNo) {
        plusOrderRefundInfoMapper.updatePaySerialNoById(id, paySerialNo);

    }

    @Override
    public void updateById(PlusOrderRefundInfoEntity entity) {
        plusOrderRefundInfoMapper.updateById(convert.toPlusOrderRefundInfoPo(entity));

    }

    @Override
    public PlusOrderRefundInfoEntity getLastOneByOrderSn(String orderSn) {
        return plusOrderRefundInfoMapper.getLastOneByOrderSn(orderSn);
    }

    @Override
    public List<PlusOrderRefundInfoEntity> getRefundInfoByStatus(Integer status, LocalDateTime time,LocalDateTime endTime,Integer lastId, Integer limit) {
        return plusOrderRefundInfoMapper.getRefundInfoByStatus(status,time,endTime,lastId,limit);
    }
}
