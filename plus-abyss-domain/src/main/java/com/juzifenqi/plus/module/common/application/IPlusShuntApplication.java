package com.juzifenqi.plus.module.common.application;

import com.juzifenqi.plus.dto.req.PageEntity;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntRouteConfigEntity;
import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierEntity;
import com.juzifenqi.plus.module.common.event.shunt.CreateSupplierEvent;
import com.juzifenqi.plus.module.common.event.shunt.CreateSupplierRouteEvent;
import com.juzifenqi.plus.module.common.event.shunt.EditSupplierPublicConfigEvent;
import com.juzifenqi.plus.module.common.event.shunt.EnableSupplierEvent;
import com.juzifenqi.plus.module.common.event.shunt.separate.CreateSeparateSupplierEvent;
import com.juzifenqi.plus.module.common.event.shunt.separate.EnableSeparateSupplierEvent;
import java.util.List;

/**
 * 分流配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/18 10:57
 */
public interface IPlusShuntApplication {

    /**
     * 新增分流主体配置
     */
    void addSupplier(CreateSupplierEvent event);

    /**
     * 编辑分流主体配置
     */
    void editSupplier(CreateSupplierEvent event);

    /**
     * 主体分页列表
     */
    PageResultEntity<PlusShuntSupplierEntity> getSupplierPageList(Integer supplierType,
            PageEntity page);

    /**
     * 根据主体类型查询主体下拉列表
     */
    List<PlusShuntSupplierEntity> getSupplierList(Integer supplierType, Integer enableState);

    /**
     * 分流主体详情
     */
    PlusShuntSupplierEntity getSupplierDetail(Integer id);

    /**
     * 停用/启用主体配置
     */
    void enableSupplier(EnableSupplierEvent event);

    /**
     * 编辑分流公共配置
     */
    void editPublicConfig(EditSupplierPublicConfigEvent event);

    /**
     * 新增分流路由配置
     */
    void addRoute(CreateSupplierRouteEvent event);

    /**
     * 编辑分流路由配置
     */
    void editRoute(CreateSupplierRouteEvent event);

    /**
     * 停用/启用分流路由配置
     */
    void enableRoute(EnableSupplierEvent event);

    /**
     * 分流配置详情
     */
    PlusShuntRouteConfigEntity getShuntDetail();

    /**
     * 新增清分主体配置
     */
    void addSeparateSupplier(CreateSeparateSupplierEvent event);

    /**
     * 编辑清分主体配置
     */
    void editSeparateSupplier(CreateSeparateSupplierEvent event);

    /**
     * 停用/启用清分主体配置
     */
    void enableSeparateSupplier(EnableSeparateSupplierEvent event);

    /**
     * 根据id查询
     */
    PlusShuntSupplierEntity loadPlusShuntSupplier(Integer id);

    /**
     * 主体额度报警
     */
    Boolean plusSupplierLimitAlarm();

    /**
     * 初始化分流主体支付配置银行账号uuid
     */
    void initSupplierBankAccountNoUuid(Integer size);

    /**
     * 检测分流主体支付配置银行账号uuid
     * @param size
     */
    void inspectSupplierBankAccountNoUuid(Integer size);

    /**
     * 刷新会员配置缓存
     *
     * @param supplierId 主体id
     * @return
     */
    void refreshSupplierCfgCache(Integer supplierId);
}
