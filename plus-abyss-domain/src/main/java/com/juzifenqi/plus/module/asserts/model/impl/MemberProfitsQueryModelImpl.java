package com.juzifenqi.plus.module.asserts.model.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.groot.utils.core.date.LocalDateTimeUtils;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.constants.PlusConstant;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.enums.BuyButtonStateEnum;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.MemberPlusStatusEnum;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.enums.PlusOrderStateEnum;
import com.juzifenqi.plus.enums.ProductOnSaleStateEnum;
import com.juzifenqi.plus.enums.VipErrorEnum;
import com.juzifenqi.plus.enums.VirtualCanBuyProductEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.model.MemberPlusQueryModel;
import com.juzifenqi.plus.module.asserts.model.MemberProfitsQueryModel;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberDetailPeriodsRepository;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusInfoDetailRepository;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusLmkVirtualRepository;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusVirtualRecordRepository;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberUseRecordRepository;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusDetailPeriodsEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusLmkVirtualEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusVirtualRecordEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberUseProductRecordEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.ProductCheckResultEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.VirtualCheckResultEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.VirtualGoodsCheckResultEntity;
import com.juzifenqi.plus.module.asserts.model.converter.IPlusMemberProfitsModelConverter;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitDetailEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitModelBasicDetailEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitVirtualProductDetailEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitVirtualProductTypeEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitVirtualProductTypeLevelEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitsCanBuyEntity;
import com.juzifenqi.plus.module.asserts.model.event.ProductCheckEvent;
import com.juzifenqi.plus.module.asserts.model.event.VirtualCheckEvent;
import com.juzifenqi.plus.module.asserts.model.event.VirtualGoodsCheckEvent;
import com.juzifenqi.plus.module.asserts.model.event.model.HandleProfitQueryEvent;
import com.juzifenqi.plus.module.asserts.repository.po.MemberPlusLmkVirtualPo;
import com.juzifenqi.plus.module.common.IProductExternalRepository;
import com.juzifenqi.plus.module.order.model.PlusOrderQueryModel;
import com.juzifenqi.plus.module.order.model.PlusOrderSnapshtoQueryModel;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderModelSnapshotEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IVirtualExternalRepository;
import com.juzifenqi.plus.module.program.model.IPlusProductQueryModel;
import com.juzifenqi.plus.module.program.model.IPlusProfitQueryModel;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramVirtualEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandVirtualProductTypeEntity;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo;
import com.juzifenqi.plus.utils.RedisLock;
import com.juzifenqi.plus.utils.RedisUtils;
import com.juzifenqi.product.entity.Product;
import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 权益相关查询
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/10 15:14
 */
@Service
@Slf4j
public class MemberProfitsQueryModelImpl implements MemberProfitsQueryModel {

    private final IPlusMemberProfitsModelConverter converter = IPlusMemberProfitsModelConverter.instance;

    @Autowired
    private IMemberPlusInfoDetailRepository    detailRepository;
    @Autowired
    private IPlusProgramQueryModel             programQueryModel;
    @Autowired
    private IMemberPlusLmkVirtualRepository    lmkVirtualRepository;
    @Autowired
    private PlusOrderQueryModel                orderQueryModel;
    @Autowired
    private IMemberUseRecordRepository         useRecordRepository;
    @Autowired
    private MemberPlusQueryModel               plusQueryModel;
    @Autowired
    private IPlusProductQueryModel             productQueryModel;
    @Autowired
    private IVirtualExternalRepository         virtualExternalRepository;
    @Autowired
    private IMemberPlusVirtualRecordRepository memberPlusVirtualRecordRepository;
    @Autowired
    private IMemberDetailPeriodsRepository     periodsRepository;
    @Autowired
    private RedisUtils                         redisUtils;
    @Autowired
    private RedisLock                          redisLock;
    @Autowired
    private IPlusProfitQueryModel              profitQueryModel;
    @Autowired
    private IProductExternalRepository         productExternalRepository;
    @Autowired
    private IMemberPlusInfoDetailRepository    memberPlusInfoDetailRepository;
    @Autowired
    private PlusOrderSnapshtoQueryModel        plusOrderSnapshtoQueryModel;


    @Override
    public VirtualCheckResultEntity canBuyVirtualProduct(VirtualCheckEvent event) {
        Integer userId = event.getUserId();
        String sku = event.getProductSku();
        Integer channelId = event.getChannelId();
        // 当前是否有待支付后付款订单（不能放try里面，会被补获）
        List<PlusOrderEntity> waitPayOrderList = orderQueryModel.getUserWaitPayOrderList(userId,
                channelId, event.getProgramId());
        if (!CollectionUtils.isEmpty(waitPayOrderList)) {
            PlusOrderEntity target = waitPayOrderList.get(0);
            Date date = new Date();
            boolean b = date.compareTo(target.getStartTime()) > 0
                    && date.compareTo(target.getEndTime()) < 0;
            if (b) {
                throw new PlusAbyssException(VipErrorEnum.PLUS_ERROR_300302);
            }
        }
        try {
            //通过查询获取当前用户是否可买权益
            VirtualCheckResultEntity result = new VirtualCheckResultEntity();
            //获取用户当前方案下生效的会员
            MemberPlusInfoDetailEntity infoDetail = detailRepository.getCurrentByProgramId(userId,
                    channelId, event.getProgramId());
            if (infoDetail == null) {
                //异常情况 不是会员
                result.setTipType(VirtualCanBuyProductEnum.CAN_BUY_EXCEPTION.getCode());
                result.setTigMsg(VirtualCanBuyProductEnum.CAN_BUY_EXCEPTION.getTipMsg());
                log.info("校验虚拟商品是否可购买会员状态校验失败:userId={},sku={}", userId, sku);
                return result;
            }
            result.setInfoDetail(infoDetail);
            //当前方案下是否有该权益
            PlusProgramVirtualEntity virtualGoods = programQueryModel.getByProgramAndSku(
                    event.getProgramId(), event.getModelId(), sku);
            if (virtualGoods == null || virtualGoods.getStatus() == CommonConstant.ZERO) {
                //异常情况 方案下未查询到当前权益
                result.setTipType(VirtualCanBuyProductEnum.CAN_BUY_EXCEPTION.getCode());
                result.setTigMsg(VirtualCanBuyProductEnum.CAN_BUY_EXCEPTION.getTipMsg());
                log.info("方案下未查询到当前权益:userId={},sku={}", userId, sku);
                return result;
            }
            result.setVirtualGoods(virtualGoods);
            result.setProfitTypeId(virtualGoods.getProfitTypeId());
            // 当前方法校验结束，暂时可买。方法外还有校验逻辑
            result.setCanBuy(CommonConstant.ONE);
            return result;
        } catch (Exception e) {
            LogUtil.printLog("查询虚拟商品是否可购买逻辑异常,userId={},sku={}", userId, sku, e);
            VirtualCheckResultEntity result = new VirtualCheckResultEntity();
            result.setTipType(VirtualCanBuyProductEnum.CAN_BUY_EXCEPTION.getCode());
            result.setTigMsg(VirtualCanBuyProductEnum.CAN_BUY_EXCEPTION.getTipMsg());
            return result;
        }
    }

    /**
     * 检查用户是否可以领取虚拟商品权益
     */
    @Override
    public VirtualGoodsCheckResultEntity canBuyVirtualGoods(VirtualGoodsCheckEvent checkEvent) {
        VirtualGoodsCheckResultEntity result = new VirtualGoodsCheckResultEntity();
        // 校验虚拟商品发送记录
        Integer userId = checkEvent.getUserId();
        String plusOrderSn = checkEvent.getPlusOrderSn();
        MemberPlusVirtualRecordEntity virtualRecord = memberPlusVirtualRecordRepository.getVirtualRecord(
                plusOrderSn, userId, PlusModelEnum.LYFF.getModelId());
        if (virtualRecord == null) {
            log.info("未查询到虚拟商品发送记录,userId:{},plusOrderSn:{}", userId, plusOrderSn);
            return result;
        }
        result.setProductId(virtualRecord.getProductId());
        result.setDiscountRate(virtualRecord.getDiscountRate());
        result.setImgUrl(virtualRecord.getImgUrl());
        result.setProductSku(virtualRecord.getSku());
        // 校验会员订单信息(支持融担卡未支付状态领取权益)
        PlusOrderEntity order = orderQueryModel.getByOrderSn(plusOrderSn);
        if (order == null) {
            log.info("会员订单号不存在,plusOrderSn:{}", plusOrderSn);
            return result;
        }
        // 校验会员订单状态!=取消状态(必须是有效的融担卡会员身份)
        if (order.getOrderState() == PlusOrderStateEnum.CANCELED.getCode()) {
            log.info("会员订单已取消,plusOrderSn:{}", plusOrderSn);
            return result;
        }
        // 校验会员订单userId和当前登录userId是否一致
        if (!userId.equals(order.getUserId())) {
            log.info("会员单非当前登录用户所属,会员订单userId:{},当前登录人userId:{}", order.getUserId(), userId);
            return result;
        }
        // 校验用户是否是会员身份
        MemberPlusInfoDetailEntity detail = memberPlusInfoDetailRepository.getByOrderSn(
                plusOrderSn);
        if (detail == null || !MemberPlusStatusEnum.NORMAL.getCode().equals(detail.getJxStatus())
                || detail.getJxEndTime().before(new Date())) {
            log.info("当前登录人非会员身份,userId:{}", userId);
            return result;
        }
        // 权益是否已充值
        List<VirtualOrders> allOrderList = virtualExternalRepository.getAllOrderList(plusOrderSn,
                userId, PlusModelEnum.LYFF.getModelId());
        if (!CollectionUtils.isEmpty(allOrderList)) {
            log.info("权益已领取,userId:{},plusOrderSn:{}", userId, plusOrderSn);
            return result;
        }
        return converter.toVirtualGoodsCheckResultEntity(virtualRecord, order.getProgramName(),
                BuyButtonStateEnum.CAN_BUY.getCode());
    }

    @Override
    public ProductCheckResultEntity canBuyProduct(ProductCheckEvent event) {
        log.info("校验会员商品是否能购买入参：{}", JSON.toJSONString(event));
        ProductCheckResultEntity result = new ProductCheckResultEntity();
        // 方案下未配置该sku
        PlusProgramProductNewPo programProduct = productQueryModel.getProgramProduct(
                event.getProductSku(), event.getProgramId(), event.getModelId());
        if (programProduct == null) {
            result.setReason("方案下未配置该商品");
            result.setBuyButtonState(BuyButtonStateEnum.RECEIVED.getCode());
            return result;
        }
        // 赋值返回，用于计算会员商品详情页的商品售价（售价 * 配置的折扣）
        result.setProductNewPo(programProduct);
        if (!Objects.equals(programProduct.getSaleState(), ProductOnSaleStateEnum.ON.getCode())) {
            result.setReason("商品已下架");
            result.setBuyButtonState(BuyButtonStateEnum.RECEIVED.getCode());
            return result;
        }
        // 没有会员单号,提示开通会员
        if (StringUtils.isBlank(event.getOrderSn())) {
            result.setReason("会员单号为空,非会员身份");
            result.setBuyButtonState(BuyButtonStateEnum.OPEN_CARD.getCode());
            return result;
        }
        if (event.getUserId() == null) {
            result.setReason("用户未登录");
            result.setBuyButtonState(BuyButtonStateEnum.OPEN_CARD.getCode());
            return result;
        }
        PlusOrderEntity order = orderQueryModel.getByOrderSn(event.getOrderSn());
        if (order == null || order.getOrderState() != PlusOrderStateEnum.PAY_SUCCESS.getCode()) {
            result.setReason("会员订单未支付");
            result.setBuyButtonState(BuyButtonStateEnum.OPEN_CARD.getCode());
            return result;
        }
        // 无效的订单号
        MemberPlusInfoDetailEntity detail = plusQueryModel.getMemberPlusInfoDetail(
                event.getOrderSn());
        if (detail == null) {
            result.setReason("会员单号无效,非会员身份");
            result.setBuyButtonState(BuyButtonStateEnum.OPEN_CARD.getCode());
            return result;
        }
        // 赋值，用于商品购买限制判断
        result.setDetail(detail);
        // 归属人不一致
        if (!Objects.equals(detail.getUserId(), event.getUserId())) {
            result.setReason("会员单非当前登录用户所属");
            result.setBuyButtonState(BuyButtonStateEnum.RECEIVED.getCode());
            return result;
        }
        Date now = new Date();
        // 非当前周期会员单,非法请求
        if (detail.getJxStartTime().after(now) || detail.getJxEndTime().before(now)) {
            result.setReason("会员单非当前周期");
            result.setBuyButtonState(BuyButtonStateEnum.RECEIVED.getCode());
            return result;
        }
        // 传入的方案id是否购买会员订单的方案id
        if (!Objects.equals(event.getProgramId(), order.getProgramId())) {
            result.setReason("传入方案与会员订单方案不一致");
            result.setBuyButtonState(BuyButtonStateEnum.RECEIVED.getCode());
            return result;
        }
        // 暂时可买，接下来还要校验分类购买限制的校验
        result.setCanBuy(true);
        return result;
    }

    @Override
    public Boolean canBuyPlusProduct(ProductCheckEvent event) {
        log.info("校验商品是否可购买开始：{}", JSON.toJSONString(event));
        Integer productId = event.getProductId();
        Integer memberId = event.getUserId();
        Integer channel = event.getChannelId();
        //判断是不是会员商品
        Boolean isPlusProductRes = profitQueryModel.isPlusProduct(productId);
        //判断是不是一元购商品
        Boolean isYygProductRes = profitQueryModel.isYygProduct(productId);
        log.info("校验商品是否可购买-商品归属信息，isPlusProductRes={}, isYygProductRes={}", isPlusProductRes,
                isYygProductRes);
        //都不是返回true
        if (!isPlusProductRes && !isYygProductRes) {
            log.info("校验商品是否可购买,校验通过,非特殊商品-memberId:{}-productId:{}", memberId, productId);
            return true;
        }
        String lockKey = RedisConstantPrefix.HALF_PRICE_SUBMIT_LOCK + memberId;
        boolean lock = redisLock.lock(lockKey, "1", 3);
        if (!lock) {
            log.info("3秒内重复请求会员商品购买校验,userId:{},key:{}", memberId, lockKey);
            throw new PlusAbyssException("短时间内不允许重复购买会员商品");
        }
        //都是，需要都判断可以不可以买，一个通过即可购买
        if (isPlusProductRes && isYygProductRes) {
            //会员商品校验
            Boolean buyPlusProduct = isCanBuyPlusProduct(memberId, channel, productId);
            //一元购校验
            Boolean buyYygProduct = isCanBuyYygProduct(event);
            log.info("校验商品是否可购买,商品同属一元购，会员商品,-memberId:{}-buyPlusProduct:{},-buyYygProduct:{}",
                    memberId, buyPlusProduct, buyYygProduct);
            return buyPlusProduct || buyYygProduct;
        }
        if (isPlusProductRes) {
            //会员商品校验
            Boolean buyPlusProduct = isCanBuyPlusProduct(memberId, channel, productId);
            log.info("校验商品是否可购买,只属于会员商品,-memberId:{}-buyPlusProduct:{}", memberId, buyPlusProduct);
            return buyPlusProduct;
        }
        //一元购校验
        Boolean buyYygProduct = isCanBuyYygProduct(event);
        log.info("校验商品是否可购买,只属于一元购商品,-memberId:{}-buyYygProduct:{}", memberId, buyYygProduct);
        return buyYygProduct;
    }

    /**
     * 是否可买一元购商品
     */
    private Boolean isCanBuyYygProduct(ProductCheckEvent event) {
        log.info("校验一元购商品是否能购买入参：{}", JSON.toJSONString(event));
        Integer productId = event.getProductId();
        Integer memberId = event.getUserId();
        boolean yygProduct = profitQueryModel.isYygProduct(productId);
        if (!yygProduct) {
            log.info("校验一元购商品是否可购买,校验通过,非特殊商品-memberId:{}-productId:{}", memberId, productId);
            return true;
        }
        return yygProductCheckLimit(memberId, event.getChannelId()).isCanBuy();
    }

    /**
     * 一元购权益商品是否能买
     */
    @Override
    public ProfitsCanBuyEntity yygProductCheckLimit(Integer userId, Integer channelId) {
        ProfitsCanBuyEntity resp = new ProfitsCanBuyEntity();
        // 是否桔会卡会员身份
        MemberPlusInfoDetailEntity detail = plusQueryModel.getUserLastInfo(userId, channelId,
                JuziPlusEnum.JH_CARD.getCode());
        if (detail == null) {
            log.info("校验一元购商品是否能买,用户非桔会卡身份：{}", userId);
            return resp;
        }
        String plusOrderSn = detail.getOrderSn();
        //设置会员类型
        resp.setConfigId(detail.getConfigId());
        //获取用户当前会员所有购买记录
        List<MemberUseProductRecordEntity> records = useRecordRepository.getUserBuyOrderRecords(
                plusOrderSn, PlusModelEnum.YYG.getModelId());
        if (CollectionUtils.isEmpty(records)) {
            //当用户未使用过一元购特权，不展示提示语（这里是可以买）
            log.info("校验一元购商品是否能买,用户未购买过,可以买：{}", userId);
            resp.setCanBuy(true);
            return resp;
        }
        //获取用户所处的周期
        MemberPlusDetailPeriodsEntity currentPeriods = plusQueryModel.getCurrentPeriods(
                plusOrderSn);
        log.info("一元购-检查购买限制-当前周期-userId={},会员单号={},currentPeriods={}", userId, plusOrderSn,
                JSON.toJSONString(currentPeriods));
        if (currentPeriods == null) {
            log.info("校验一元购商品是否能买,会员单当前周期为空：{},{}", userId, plusOrderSn);
            return resp;
        }
        Date startTime = currentPeriods.getStartTime();
        Date endTime = currentPeriods.getEndTime();
        //当前周期是否买过
        List<MemberUseProductRecordEntity> currentRecords = records.stream()
                .filter(record -> record.getOrderTime().after(startTime) && record.getOrderTime()
                        .before(endTime)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(currentRecords)) {
            log.info("校验一元购商品是否能买,用户当前周期没买过,可以买：{},{}", userId, plusOrderSn);
            //用户使用过一元购特权，当前周期没购买（这里是可以买）
            resp.setCanBuy(true);
            resp.setShowType(1);
            return resp;
        }
        //当前周期已购买
        MemberUseProductRecordEntity currentRecord = currentRecords.get(0);
        Date useDate = currentRecord.getOrderTime();
        log.info("一元购-检查购买限制-已购买日期-userId={},会员单号={},useDate={}", userId, plusOrderSn, useDate);
        //当前周期购买过，判断是否还有剩余周期可购买
        //获取用户下一个周期
        MemberPlusDetailPeriodsEntity nextPeriods = plusQueryModel.getNextPeriods(plusOrderSn);
        log.info("一元购-检查购买限制-下一个周期-userId={},会员单号={},nextPeriods={}", userId, plusOrderSn,
                JSON.toJSONString(nextPeriods));
        if (nextPeriods == null) {
            log.info("校验一元购商品是否能买,会员单没有下个周期,不可以买：{},{}", userId, plusOrderSn);
            //当用户使用过一元购特权，没有下个周期（这里是不可以买）
            resp.setShowType(2);
            resp.setUseDate(useDate);
            return resp;
        }
        //设置下个周期开始时间（这里是不可以买，要等下个周期）
        resp.setShowType(3);
        resp.setNextCanUseDate(nextPeriods.getStartTime());
        resp.setUseDate(useDate);
        log.info("一元购-检查购买限制-结束-userId={},会员单号={},返回：{}", userId, plusOrderSn,
                JSON.toJSONString(resp));
        return resp;
    }

    /**
     * 校验会员商品是否可购买（是不是桔会卡或桔享）
     */
    private boolean isCanBuyPlusProduct(Integer memberId, Integer channel, Integer productId) {
        List<MemberPlusInfoEntity> allInfos = plusQueryModel.getMemberPlusInfoList(memberId,
                channel);
        log.info("下单校验会员商品是否可买-会员信息-userId={}-productId={},plus={}", memberId, productId,
                JSON.toJSONString(allInfos));
        if (CollectionUtils.isEmpty(allInfos)) {
            log.info("下单校验会员商品是否可买-当前用户不是会员:memberId：{} ", memberId);
            return false;
        }
        List<MemberPlusInfoEntity> collect = allInfos.stream().filter(plusInfo ->
                        plusInfo.getConfigId().equals(JuziPlusEnum.NEW_JUXP_CARD.getCode())
                                || plusInfo.getConfigId().equals(JuziPlusEnum.JH_CARD.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            log.info("下单校验会员商品是否可买-用户未开通桔享或桔会:memberId：{} ", memberId);
            return false;
        }
        //查询productId是否为一元钱的商品
        Product productById = productExternalRepository.getProductById(productId);
        if (productById == null) {
            log.info("检查会员商品是否可买,商品为空-memberId:{}-productId:{}", memberId, productId);
            return false;
        }
        if (CommonConstant.ZERO != productById.getMallMobilePrice().compareTo(BigDecimal.ONE)) {
            log.info("检查会员商品是否可买,非一元商品-通过-memberId:{}-productId:{}", memberId, productId);
            return true;
        }
        // 只有桔享卡有一元钱商品限制
        MemberPlusInfoDetailEntity info = plusQueryModel.getUserFirstInfo(memberId, channel,
                JuziPlusEnum.NEW_JUXP_CARD.getCode());
        if (Objects.isNull(info)) {
            log.info("检查一元商品是否可买,非桔享卡用户-通过-memberId:{}-productId:{}", memberId, productId);
            return true;
        }
        //判断当前桔享会员是否可买一元商品
        boolean canBuyYyProduct = isCanBuyYyProduct(memberId, info.getOrderSn());
        log.info("检查会员商品是否可买结束,结果-memberId:{}-productId:{}-canBuyYyProduct:{}", memberId, productId,
                canBuyYyProduct);
        return canBuyYyProduct;
    }

    /**
     * 检查用户当前周期是否买过一元会员商品
     */
    private boolean isCanBuyYyProduct(Integer userId, String plusOrderSn) {
        log.info("检查用户当前周期是否买过一元商品-开始-userId={}-plusOrderSn={}", userId, plusOrderSn);
        //获取用户当前会员所有购买记录
        List<MemberUseProductRecordEntity> records = useRecordRepository.getUserBuyOrderRecords(
                plusOrderSn, PlusModelEnum.BJSP.getModelId());
        log.info("获取用户当前会员所有一元商品购买记录-res={}", JSON.toJSONString(records));
        if (CollectionUtils.isEmpty(records)) {
            //当用户未买过一元商品
            return true;
        }
        //获取会员单当前周期
        MemberPlusDetailPeriodsEntity currentPeriods = plusQueryModel.getCurrentPeriods(
                plusOrderSn);
        log.info("一元商品-检查购买限制-当前周期-userId={},会员单号={},currentPeriods={}", userId, plusOrderSn,
                JSON.toJSONString(currentPeriods));
        if (currentPeriods == null) {
            return false;
        }
        Date startTime = currentPeriods.getStartTime();
        Date endTime = currentPeriods.getEndTime();
        //当前周期是否买过一块钱商品
        List<MemberUseProductRecordEntity> currentRecords = records.stream()
                .filter(record -> record.getOrderTime().after(startTime) && record.getOrderTime()
                        .before(endTime) && CommonConstant.ZERO == record.getOrderMoney()
                        .compareTo(BigDecimal.ONE)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(currentRecords)) {
            //用户使用过一元商品，但当前周期没购买
            log.info("用户使用过一元商品，但当前周期没购买-userId={},会员单号={}", userId, plusOrderSn);
            return true;
        }
        log.info("用户使用过一元商品，当前周期不可购买-userId={},会员单号={}", userId, plusOrderSn);
        return false;
    }

    @Override
    public MemberPlusLmkVirtualEntity getMemberLmkVirtualByOrderSn(String orderSn) {
        MemberPlusLmkVirtualPo po = lmkVirtualRepository.getByOrderSn(orderSn);
        return converter.toMemberPlusLmkVirtualEntity(po);
    }

    @Override
    public List<MemberUseProductRecordEntity> getUserBuyOrderRecords(String orderSn,
            Integer modelId) {
        return useRecordRepository.getUserBuyOrderRecords(orderSn, modelId);
    }

    /**
     * 权益页下的虚拟商品展示
     */
    @Override
    public List<ProfitVirtualProductTypeEntity> getProfitVirtualProductTypeEntityList(
            HandleProfitQueryEvent event) {
        Integer programId = event.getProgramId();
        Integer memberId = event.getUserId();
        String orderSn = event.getOrderSn();
        log.info("获取权益页下的虚拟商品展示信息开始:programId={}， memberId={}，orderSn={}", programId, memberId,
                orderSn);
        try {
            String redisKey =
                    RedisConstantPrefix.MEMBER_VIRTUAL_PRODUCTS + programId + "_" + memberId + "_"
                            + orderSn;
            if (redisUtils.hasKey(redisKey)) {
                List<ProfitVirtualProductTypeEntity> virtuals = JSONObject.parseArray(
                        redisUtils.get(redisKey), ProfitVirtualProductTypeEntity.class);
                log.info("缓存中获取取权益页下的虚拟商品展示信息:{}", JSONObject.toJSON(virtuals));
                return virtuals;
            }
            //从缓存中获取当前方案的虚拟商品信息
            List<LandVirtualProductTypeEntity> virtualProductList = profitQueryModel.getVirtualProductList(
                    programId);
            List<ProfitVirtualProductTypeEntity> types = converter.toProfitVirtualProductTypeEntityList(
                    virtualProductList);
            //通过当前会员开通的所有单子
            List<VirtualOrders> ordersByPlus = virtualExternalRepository.countOrderByPlus(
                    event.getOrderSn(), PlusModelEnum.SHQY.getModelId());
            if (ordersByPlus.size() <= CommonConstant.ZERO) {
                log.info("权益页展示-当前会员没买过商品:orderSn={}，memberId={}", orderSn, memberId);
                //没买过商品不用设置标签信息
                redisUtils.setEx(redisKey, JSON.toJSONString(types), 30, TimeUnit.SECONDS);
                return types;
            }
            // 20230523 zjf 移除联名权益
            MemberPlusLmkVirtualPo virtual = lmkVirtualRepository.getByOrderSn(orderSn);
            if (virtual != null && org.apache.commons.lang3.StringUtils.isNotBlank(
                    virtual.getVirtualOrderSn())) {
                log.info("权益页展示-生活权益限制-联名权益判断开始：{}", orderSn);
                ordersByPlus.removeIf(e -> virtual.getVirtualOrderSn().equals(e.getOrderSn()));
                log.info("权益页展示-生活权益限制-联名权益判断移除后结果：{}", JSONObject.toJSONString(ordersByPlus));
            }
            //处理标签信息
            types.forEach(type -> {
                if (type.getNumLimit() <= CommonConstant.ZERO) {
                    //当前类型不限制,不进行处理 直接continue
                    return;
                }
                //周期（月或整个周期）内，当前类下面买的商品
                List<String> receiveSku = new ArrayList<>();
                //领取状态-0 未领取 1 本月已领 2 周期已领
                int receiveState;
                if (type.getNumLimit() == CommonConstant.ONE) {
                    receiveState = CommonConstant.ONE;
                    //当月的开始时间和结束时间
                    Date startTime = LocalDateTimeUtils.firstDayOfMonth();
                    Date endTime = LocalDateTimeUtils.lastDayOfMonth();
                    receiveSku = ordersByPlus.stream()
                            .filter(order -> startTime.before(order.getCreateTime())
                                    && endTime.after(order.getCreateTime()) && type.getId()
                                    .equals(order.getProfitTypeId()))
                            .map(VirtualOrders::getProductSku).distinct()
                            .collect(Collectors.toList());
                } else {
                    receiveState = CommonConstant.TWO;
                    receiveSku = ordersByPlus.stream()
                            .filter(order -> type.getId().equals(order.getProfitTypeId()))
                            .map(VirtualOrders::getProductSku).distinct()
                            .collect(Collectors.toList());
                }
                log.info("周期内，当前类下面买的商品:memberId={},product={},receiveState={}", memberId,
                        JSONObject.toJSON(receiveSku), receiveState);
                if (receiveSku.size() <= CommonConstant.ZERO) {
                    //当前周期（月或整个周期）没买过这个分类的商品
                    return;
                }
                List<ProfitVirtualProductDetailEntity> productsInfo = type.getProductsInfo();
                for (ProfitVirtualProductDetailEntity product : productsInfo) {
                    if (receiveSku.contains(product.getSku())) {
                        product.setReceiveState(receiveState);
                    }
                }
            });
            log.info("逻辑获取取权益页下的虚拟商品展示信息:{}", JSONObject.toJSON(types));
            redisUtils.setEx(redisKey, JSON.toJSONString(types), 30, TimeUnit.SECONDS);
            return types;
        } catch (Exception e) {
            LogUtil.printLog("获取权益页下的虚拟商品展示信息异常：programId={}，memberId={}", programId, memberId, e);
            return null;
        }
    }

    /**
     * 权益页下的虚拟商品展示-多级分类
     */
    @Override
    public List<ProfitVirtualProductTypeLevelEntity> getProfitVirtualProductTypeLevelEntityList(
            HandleProfitQueryEvent event, ProfitDetailEntity entity) {
        Integer programId = event.getProgramId();
        Integer modelId = event.getModelId();
        Integer memberId = event.getUserId();
        String orderSn = event.getOrderSn();
        log.info("获取权益页下的多级分类虚拟商品展示信息开始:programId={}，modelId={}， memberId={}，orderSn={}", programId,
                modelId, memberId, orderSn);
        try {
            String redisKey =
                    RedisConstantPrefix.MEMBER_VIRTUAL_PRODUCTS + programId + "_" + modelId + "_"
                            + memberId + "_" + orderSn;
            if (redisUtils.hasKey(redisKey)) {
                List<ProfitVirtualProductTypeLevelEntity> virtuals = JSONObject.parseArray(
                        redisUtils.get(redisKey), ProfitVirtualProductTypeLevelEntity.class);
                log.info("缓存中获取权益页下的多级分类虚拟商品展示信息:{}", JSONObject.toJSON(virtuals));
                return virtuals;
            }
            //从缓存中获取当前方案的虚拟商品信息
            List<ProfitVirtualProductTypeLevelEntity> types = getProgramVirtualProfitVos(programId,
                    modelId);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(types)) {
                return types;
            }
            // 领取时间格式
            String receiveFormat = "yyyy年MM月dd日HH时";
            MemberPlusDetailPeriodsEntity currentPeriods = null;
            for (ProfitVirtualProductTypeLevelEntity typeOne : types) {
                if (org.apache.commons.collections.CollectionUtils.isEmpty(
                        typeOne.getVirtualTypeVos())) {
                    // 一级分类下无子分类,直接continue
                    continue;
                }
                if (typeOne.getVirtualTypeVos().stream()
                        .filter(x -> x.getNumLimit() == CommonConstant.THREE).findFirst()
                        .orElse(null) != null) {
                    // 存在周期分类
                    // 获取用户所处的周期
                    currentPeriods = periodsRepository.getCurrentPeriods(orderSn);
                    break;
                }
            }
            // 领取时间处理
            MemberPlusDetailPeriodsEntity finalCurrentPeriods = currentPeriods;
            types.forEach(typeOne -> {
                if (org.apache.commons.collections.CollectionUtils.isEmpty(
                        typeOne.getVirtualTypeVos())) {
                    // 一级分类下无子分类,直接continue
                    return;
                }
                typeOne.getVirtualTypeVos().forEach(type -> {
                    // 0_无限制 2_限会员有效期购买一次 3_每周期购买一次
                    // 除了按周期限制，其余都显示会员当前周期
                    if (type.getNumLimit() == CommonConstant.THREE) {
                        // 获取用户所处的周期
                        if (finalCurrentPeriods == null) {
                            return;
                        }
                        Date startTime = finalCurrentPeriods.getStartTime();
                        Date endTime = finalCurrentPeriods.getEndTime();
                        // 领取周期 到小时
                        type.setReceiveStartTime(
                                LocalDateTimeUtils.parseDateToString(startTime, receiveFormat));
                        type.setReceiveEndTime(
                                LocalDateTimeUtils.parseDateToString(endTime, receiveFormat));
                    } else {
                        // 领取周期 到小时 会员当前周期
                        type.setReceiveStartTime(LocalDateTimeUtils.parseDateToString(
                                entity.getPlusDetailInfo().getCurrentStartTime(), receiveFormat));
                        type.setReceiveEndTime(LocalDateTimeUtils.parseDateToString(
                                entity.getPlusDetailInfo().getCurrentEndTime(), receiveFormat));
                    }
                });
            });
            //通过当前会员开通的所有单子
            List<VirtualOrders> ordersByPlus = virtualExternalRepository.countOrderByPlus(orderSn,
                    modelId);
            if (ordersByPlus.size() <= CommonConstant.ZERO) {
                log.info("权益页展示-当前会员没买过商品:orderSn={}，memberId={}", orderSn, memberId);
                //没买过商品不用设置标签信息
                redisUtils.setEx(redisKey, JSON.toJSONString(types), 30, TimeUnit.SECONDS);
                return types;
            }
            // 20230523 zjf 移除联名权益
            MemberPlusLmkVirtualPo virtual = lmkVirtualRepository.getByOrderSn(orderSn);
            if (virtual != null && org.apache.commons.lang3.StringUtils.isNotBlank(
                    virtual.getVirtualOrderSn())) {
                log.info("权益页展示-多级分类生活权益限制-联名权益判断开始：{}", orderSn);
                ordersByPlus.removeIf(e -> virtual.getVirtualOrderSn().equals(e.getOrderSn()));
                log.info("权益页展示-多级分类生活权益限制-联名权益判断移除后结果：{}", JSONObject.toJSONString(ordersByPlus));
            }
            //处理标签信息
            types.forEach(typeOne -> {
                // 子分类处理
                if (org.apache.commons.collections.CollectionUtils.isEmpty(
                        typeOne.getVirtualTypeVos())) {
                    // 一级分类下无子分类,直接continue
                    return;
                }
                typeOne.getVirtualTypeVos().forEach(type -> {
                    if (type.getNumLimit() <= CommonConstant.ZERO) {
                        //当前类型不限制,不进行处理 直接continue
                        return;
                    }
                    // 限购的情况下才展示【去预定酒店】按钮
                    setHotelOrderBtn(type, ordersByPlus);
                    // 0_无限制 2_限会员有效期购买一次 3_每周期购买一次
                    // 周期（2会员周期或者3每周期30天）内，当前类下面买的商品
                    List<String> receiveSku = new ArrayList<>();
                    // 领取状态-0 未领取 2 会员周期已领 3 每周期已领
                    int receiveState = CommonConstant.ZERO;
                    if (type.getNumLimit() == CommonConstant.TWO) {
                        // 每会员周期
                        receiveState = CommonConstant.TWO;
                        receiveSku = ordersByPlus.stream()
                                .filter(order -> type.getId().equals(order.getProfitTypeId()))
                                .map(VirtualOrders::getProductSku).distinct()
                                .collect(Collectors.toList());
                    } else if (type.getNumLimit() == CommonConstant.THREE) {
                        // 每周期30天
                        receiveState = CommonConstant.THREE;
                        // 获取用户所处的周期
                        if (finalCurrentPeriods == null) {
                            return;
                        }
                        Date startTime = finalCurrentPeriods.getStartTime();
                        Date endTime = finalCurrentPeriods.getEndTime();
                        receiveSku = ordersByPlus.stream()
                                .filter(order -> startTime.before(order.getCreateTime())
                                        && endTime.after(order.getCreateTime()) && type.getId()
                                        .equals(order.getProfitTypeId()))
                                .map(VirtualOrders::getProductSku).distinct()
                                .collect(Collectors.toList());
                    }
                    log.info("周期内，当前类下面买的商品:memberId={},product={},receiveState={}", memberId,
                            JSONObject.toJSON(receiveSku), receiveState);
                    if (receiveSku.size() == CommonConstant.ZERO) {
                        //当前周期（月或整个周期）没买过这个分类的商品
                        return;
                    }
                    List<ProfitVirtualProductDetailEntity> productsInfo = type.getProductsInfo();
                    for (ProfitVirtualProductDetailEntity product : productsInfo) {
                        // 设置领取状态
                        if (receiveSku.contains(product.getSku())) {
                            product.setReceiveState(receiveState);
                        }
                    }
                });
            });
            log.info("逻辑获取取权益页下的虚拟商品展示信息:{}", JSONObject.toJSON(types));
            redisUtils.setEx(redisKey, JSON.toJSONString(types), 30, TimeUnit.SECONDS);
            return types;
        } catch (Exception e) {
            LogUtil.printLog("获取权益页下的虚拟商品展示信息异常：programId={}，modelId={}，memberId={}", programId,
                    modelId, memberId, e);
            return null;
        }
    }

    /**
     * 获取方案下多级分类关联虚拟商品信息
     *
     * @param programId 方案id
     * @param modelId 权益id
     */
    private List<ProfitVirtualProductTypeLevelEntity> getProgramVirtualProfitVos(Integer programId,
            Integer modelId) {
        log.info("获取方案下多级分类关联虚拟商品信息开始:programId={} modelId={}", programId, modelId);
        List<ProfitVirtualProductTypeLevelEntity> virtuals = new ArrayList<>();
        try {
            //从缓存中获取虚拟商品信息
            //huxf 2023.11.20 增加权益ID入参
            String redisKey =
                    RedisConstantPrefix.PROGRAM_VIRTUAL_PRODUCTS_LEVEL + programId + "_" + modelId;
            String cacheRes = redisUtils.get(redisKey);
            if (!StringUtils.isEmpty(cacheRes)) {
                virtuals = JSONObject.parseArray(cacheRes,
                        ProfitVirtualProductTypeLevelEntity.class);
            }
            log.info("缓存中获取方案下多级分类虚拟商品,programId={} modelId={}，result:{}", programId, modelId,
                    JSON.toJSONString(virtuals));
            return virtuals;
        } catch (Exception e) {
            LogUtil.printLog("获取方案下多级分类关联虚拟商品信息异常programId={} modelId={}", programId, modelId, e);
            return virtuals;
        }
    }

    /**
     * 设置酒店订单按钮状态
     */
    private void setHotelOrderBtn(ProfitVirtualProductTypeLevelEntity type,
            List<VirtualOrders> list) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(type.getProductsInfo())) {
            log.info("设置酒店订单按钮状态当前分类下无商品列表");
            return;
        }
        // 处理去预定酒店按钮
        list.stream().filter(e -> e.getProfitTypeId().equals(type.getId())
                        && e.getHotelOrderBtn() == CommonConstant.ONE)
                .forEach(e -> type.getProductsInfo().forEach(p -> {
                    if (p.getSku().equals(e.getProductSku())) {
                        p.setHotelOrderBtn(e.getHotelOrderBtn());
                        p.setVirtualOrderSn(e.getOrderSn());
                    }
                }));
    }

    @Override
    public ProfitModelBasicDetailEntity getProfitBasicInfo(HandleProfitQueryEvent queryEvent) {
        ProfitModelBasicDetailEntity profitBasicInfo = null;
        if (PlusConstant.MERGE_CARD_LIST.contains(queryEvent.getConfigId())
                && StringUtils.isNotBlank(queryEvent.getOrderSn())
                && Objects.nonNull(queryEvent.getModelId())) {
            log.info("获取会员方案权益快照信息，event {}", JSONObject.toJSON(queryEvent));
            PlusOrderModelSnapshotEntity entity = plusOrderSnapshtoQueryModel.getModelSnapshotByOrderSnAndModelId(
                    queryEvent.getOrderSn(), queryEvent.getModelId());
            if (Objects.nonNull(entity)) {
                profitBasicInfo = converter.toProfitModelBasicDetailEntity(entity);
                profitBasicInfo.setShortPY(PlusModelEnum.getShortPYById(queryEvent.getModelId()));
            }
        }
        if (Objects.isNull(profitBasicInfo)) {
            log.info("获取会员方案权益信息，event {}", JSONObject.toJSON(queryEvent));
            profitBasicInfo = profitQueryModel.getProfitBasicInfo(queryEvent);
        }
        return profitBasicInfo;
    }
}
