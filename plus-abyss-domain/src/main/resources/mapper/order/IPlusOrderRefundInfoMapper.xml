<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderRefundInfoMapper">

    <resultMap id="PlusOrderRefundInfo" type="com.juzifenqi.plus.module.order.repository.po.PlusOrderRefundInfoPo" >
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="refund_serial_no" property="refundSerialNo" />
        <result column="pay_serial_no" property="paySerialNo" />
        <result column="refund_state" property="refundState" />
        <result column="loan_order_sn" property="loanOrderSn" />
        <result column="refund_type" property="refundType" />
        <result column="cancel_type" property="cancelType"/>
        <result column="refund_rate" property="refundRate" />
        <result column="cancel_reason" property="cancelReason" />
        <result column="batch_cancel" property="batchCancel" />
        <result column="need_notice" property="needNotice" />
        <result column="remark" property="remark" />
        <result column="pay_callback_time" property="payCallbackTime"/>
        <result column="opt_user_id" property="optUserId" />
        <result column="opt_user_name" property="optUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `refund_serial_no`,
        `pay_serial_no`,
        `refund_state`,
        `loan_order_sn`,
        `refund_type`,
        `cancel_type`,
        `refund_rate`,
        `cancel_reason`,
        `batch_cancel`,
        `need_notice`,
        `remark`,
        `pay_callback_time`,
        `opt_user_id`,
        `opt_user_name`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="save" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderRefundInfoPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_refund_info (`order_sn`,
                                            `refund_serial_no`,
                                            `pay_serial_no`,
                                            `refund_state`,
                                            `loan_order_sn`,
                                            `refund_type`,
                                            `cancel_type`,
                                            `refund_rate`,
                                            `cancel_reason`,
                                            `batch_cancel`,
                                            `need_notice`,
                                            `remark`,
                                            `pay_callback_time`,
                                            `opt_user_id`,
                                            `opt_user_name`,
                                            `create_time`,
                                            `update_time`)
        VALUES (#{orderSn},
                #{refundSerialNo},
                #{paySerialNo},
                #{refundState},
                #{loanOrderSn},
                #{refundType},
                #{cancelType},
                #{refundRate},
                #{cancelReason},
                #{batchCancel},
                #{needNotice},
                #{remark},
                #{payCallbackTime},
                #{optUserId},
                #{optUserName},
                NOW(),
                #{updateTime})
    </insert>


    <insert id="insertPlusOrderRefundInfo" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderRefundInfoPo">
        INSERT INTO plus_order_refund_info (
            `order_sn`,
            `refund_serial_no`,
            `pay_serial_no`,
            `refund_state`,
            `loan_order_sn`,
            `refund_type`,
            `cancel_type`,
            `refund_rate`,
            `cancel_reason`,
            `batch_cancel`,
            `need_notice`,
            `remark`,
            `pay_callback_time`,
            `opt_user_id`,
            `opt_user_name`,
            `create_time`,
            `update_time`
        )
        VALUES(
                  #{plusOrderRefundInfo.orderSn},
                  #{plusOrderRefundInfo.refundSerialNo},
                  #{plusOrderRefundInfo.paySerialNo},
                  #{plusOrderRefundInfo.refundState},
                  #{plusOrderRefundInfo.loanOrderSn},
                  #{plusOrderRefundInfo.refundType},
                  #{plusOrderRefundInfo.cancelType},
                  #{plusOrderRefundInfo.refundRate},
                  #{plusOrderRefundInfo.cancelReason},
                  #{plusOrderRefundInfo.batchCancel},
                  #{plusOrderRefundInfo.needNotice},
                  #{plusOrderRefundInfo.remark},
                  #{plusOrderRefundInfo.payCallbackTime},
                  #{plusOrderRefundInfo.optUserId},
                  #{plusOrderRefundInfo.optUserName},
                  NOW(),
                  #{plusOrderRefundInfo.updateTime}
              )
    </insert>

    <update id="updateById">
        UPDATE plus_order_refund_info
        SET
        <if test="refundState != null">
            `refund_state`= #{refundState},
        </if>
        <if test="paySerialNo != null">
            `pay_serial_no`= #{paySerialNo},
        </if>
        <if test="payCallbackTime != null">
            `pay_callback_time`= #{payCallbackTime},
        </if>
        update_time = now()
        WHERE `id` = #{id}
    </update>

    <update id="updateRefundState">
        UPDATE plus_order_refund_info
        SET `refund_state`= #{refundState},
            update_time   = now()
        WHERE refund_serial_no = #{refundSerialNo}
    </update>

    <select id="getByOrderSn" resultMap="PlusOrderRefundInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_refund_info
        WHERE `order_sn` = #{orderSn}
        and refund_state in
        <foreach collection="refundStateList" item="refundState" open="(" close=")" separator=",">
            #{refundState}
        </foreach>
    </select>
    <select id="getByRefundSerialNo"
            resultMap="PlusOrderRefundInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_refund_info
        WHERE `refund_serial_no` = #{refundSerialNo}
    </select>

    <select id="getById"
            resultMap="PlusOrderRefundInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_refund_info
        WHERE `id` = #{id}
    </select>

    <update id="updatePaySerialNoById">
        UPDATE plus_order_refund_info
        SET `pay_serial_no`= #{paySerialNo},
            update_time   = now()
        WHERE `id` = #{id}
    </update>

    <update id="updatePaySerialNo">
        UPDATE plus_order_refund_info
        SET `pay_serial_no`= #{paySerialNo},
            update_time   = now()
        WHERE `refund_serial_no` = #{refundSerialNo}
    </update>

    <select id="getLastOneByOrderSn"
            resultType="com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity">
        select
        <include refid="Base_Column_List"/>
        from plus_order_refund_info
        where order_sn = #{orderSn}
        order by id desc
        limit 1
    </select>


    <select id="getRefundInfoByStatus" resultType="com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity">
        select
        <include refid="Base_Column_List"/>
        from plus_order_refund_info
        where refund_state = #{status}
        and create_time &lt;= #{time}
        and create_time &gt;= #{endTime}
        <if test="lastId != null">
            and id &gt; #{lastId}
        </if>
        order by create_time asc
        limit #{limit}
    </select>
</mapper>
