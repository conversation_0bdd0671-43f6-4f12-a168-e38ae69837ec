<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.6.8</version>
    </parent>

    <groupId>com.juzifenqi</groupId>
    <artifactId>plus-abyss</artifactId>
    <packaging>pom</packaging>
    <version>ykd-1.1.5-SNAPSHOT</version>

    <modules>
        <module>plus-abyss-domain</module>
        <module>plus-abyss-api</module>
        <module>plus-abyss-config</module>
        <module>plus-abyss-external</module>
        <module>plus-abyss-job</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <skipTests>true</skipTests>
        <plus.abyss.api.version>ykd-1.1.5-SNAPSHOT</plus.abyss.api.version>
        <plus.magic.api.version>ykd-1.0.2</plus.magic.api.version>
        <plus.magic.bean.version>ykd-1.0.2</plus.magic.bean.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.juzifenqi</groupId>
                <artifactId>plus-abyss-api</artifactId>
                <version>${plus.abyss.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.juzifenqi</groupId>
                <artifactId>magic-api</artifactId>
                <version>${plus.magic.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.juzifenqi</groupId>
                <artifactId>magic-bean</artifactId>
                <version>${plus.magic.bean.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>SUDAIBEAR Release Repository</name>
            <url>http://nexus.juhuiwangluokeji.com:8081/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>SUDAIBEAR Snapshot Repository</name>
            <url>http://nexus.juhuiwangluokeji.com:8081/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>nexus</id>
            <name>Public Repositories</name>
            <url>http://nexus.juhuiwangluokeji.com:8081/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>thirdparty</id>
            <url>http://nexus.juhuiwangluokeji.com:8081/nexus/content/repositories/thirdparty</url>
            <layout>default</layout>
        </repository>
    </repositories>

</project>
