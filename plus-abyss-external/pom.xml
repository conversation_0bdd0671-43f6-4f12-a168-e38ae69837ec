<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.juzifenqi</groupId>
        <artifactId>plus-abyss</artifactId>
        <version>ykd-1.1.5-SNAPSHOT</version>
    </parent>

    <artifactId>plus-abyss-external</artifactId>
    <version>ykd-1.1.5-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>plus-abyss-config</artifactId>
            <version>ykd-1.1.5-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>magic-bean</artifactId>
        </dependency>
        <!-- 资金 -->
        <dependency>
            <groupId>com.yikoudai</groupId>
            <artifactId>cove-open-api</artifactId>
            <version>1.0.1.open-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.yikoudai</groupId>
            <artifactId>cove-open-entity</artifactId>
            <version>1.0.1.open-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
